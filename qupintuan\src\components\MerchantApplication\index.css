/* src/components/MerchantApplication/index.css */

.merchant-application {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

/* 商家布局 - 左右分栏 */
.merchant-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
}

/* 左侧商家信息卡片 */
.merchant-info-card {
  flex: 1;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #10b981;
  overflow: hidden;
  width: 100%;
}

.merchant-info-card .card-header {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.merchant-info-card .card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.merchant-info-card .card-content {
  padding: 20px;
}

.merchant-info-card .basic-info-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.merchant-info-card .basic-info-grid .info-item {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  padding: 8px 0 !important;
  border-bottom: 1px solid #f1f5f9 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  flex-direction: row !important;
}

.merchant-info-card .basic-info-grid .info-item:last-child {
  border-bottom: none !important;
}

.merchant-info-card .basic-info-grid .info-item .label {
  font-weight: 500 !important;
  color: #64748b !important;
  font-size: 14px !important;
  flex-shrink: 0 !important;
  width: 80px !important;
  margin-right: 8px !important;
  margin-bottom: 0 !important;
}

.merchant-info-card .basic-info-grid .info-item .label::after {
  content: "：";
}

.merchant-info-card .basic-info-grid .info-item .value,
.merchant-info-card .basic-info-grid .info-item .status {
  color: #1e293b !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  flex: 1 !important;
  margin-bottom: 0 !important;
}

.merchant-info-card .basic-info-grid .info-item .status.active {
  color: #10b981 !important;
}

.merchant-info-card .basic-info-grid .info-item .status.inactive {
  color: #f59e0b !important;
}

.merchant-info-card .basic-info-grid .info-item .status.verified {
  color: #10b981 !important;
}

.merchant-info-card .basic-info-grid .info-item .status.unverified {
  color: #f59e0b !important;
}

/* 右侧操作区域 */
.merchant-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  width: 100%;
  box-sizing: border-box;
}

.action-card h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.action-card p {
  margin: 0 0 16px 0;
  color: #64748b;
  line-height: 1.5;
}

.action-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-button.primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.action-button.primary:hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.status-indicator {
  background: #fef3c7;
  color: #92400e;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.status-indicator.pending {
  background: #fef3c7;
  color: #92400e;
}

/* 经营数据网格 */
.data-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.data-label {
  font-weight: 500;
  color: #64748b;
}

.data-value {
  font-weight: 600;
  color: #1e293b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .merchant-layout {
    flex-direction: column;
  }

  .merchant-info-card {
    flex: none;
  }

  .merchant-info-card .basic-info-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
  }

  .merchant-info-card .basic-info-grid .info-item {
    display: flex !important;
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #f1f5f9 !important;
    flex-direction: row !important;
  }

  .merchant-info-card .basic-info-grid .info-item .label {
    font-size: 13px !important;
    width: 80px !important;
    margin-right: 8px !important;
    margin-bottom: 0 !important;
  }

  .merchant-info-card .basic-info-grid .info-item .value,
  .merchant-info-card .basic-info-grid .info-item .status {
    font-size: 14px !important;
    flex: 1 !important;
    margin-bottom: 0 !important;
  }
}

@media (max-width: 480px) {
  .merchant-info-card .basic-info-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
  }

  .merchant-info-card .basic-info-grid .info-item {
    display: flex !important;
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 6px 0 !important;
    border-bottom: 1px solid #f1f5f9 !important;
    flex-direction: row !important;
  }

  .merchant-info-card .basic-info-grid .info-item .label {
    font-size: 12px !important;
    width: 70px !important;
    margin-right: 8px !important;
    margin-bottom: 0 !important;
  }

  .merchant-info-card .basic-info-grid .info-item .value,
  .merchant-info-card .basic-info-grid .info-item .status {
    font-size: 13px !important;
    flex: 1 !important;
    margin-bottom: 0 !important;
  }
}

.application-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.application-card.merchant-info {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.application-header {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.application-card.merchant-info .application-header {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.application-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
}

.application-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.application-form {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.form-section .section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 15px;
  transition: all 0.2s ease;
  box-sizing: border-box;
  font-family: inherit;
  background: white;
}

.form-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: #ef4444;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.error-message {
  margin-top: 8px;
  color: #ef4444;
  font-size: 14px;
}

.input-hint {
  margin-top: 6px;
  color: #6b7280;
  font-size: 13px;
}

/* 图片上传组件样式 */
.image-upload-container {
  width: 100%;
}

.upload-area {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-width: 160px;
  justify-content: center;
}

.upload-button:hover:not(.uploading) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.upload-button.uploading {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
}

.upload-icon {
  font-size: 16px;
}

.upload-spinner {
  font-size: 16px;
  animation: spin 1s linear infinite;
}

.image-preview {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
  background: white;
}

.preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  display: block;
}

.remove-image {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-image:hover {
  background: #ef4444;
  transform: scale(1.1);
}

.upload-hint {
  margin-top: 8px;
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
}

.application-notice {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.application-notice h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #374151;
}

.application-notice ul {
  margin: 0;
  padding-left: 20px;
  color: #6b7280;
}

.application-notice li {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.5;
}

.submit-btn {
  width: 100%;
  padding: 14px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:active:not(:disabled) {
  transform: translateY(0);
}

.submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn.loading {
  background: #6b7280;
  cursor: not-allowed;
}

.submit-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 需求提示样式 */
.requirement-notice {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: 12px;
  padding: 20px;
  margin: 24px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.notice-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.notice-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #92400e;
}

.notice-content p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #92400e;
  line-height: 1.5;
}

.notice-content p:last-child {
  margin-bottom: 0;
}



/* 状态消息样式 */
.status-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border-radius: 12px;
  font-weight: 600;
  text-align: center;
}

.status-message .status-icon {
  font-size: 20px;
}

.status-message .status-text {
  font-size: 16px;
}



/* 响应式设计 - 只在真正的移动设备上应用 */
@media (max-width: 768px) and (pointer: coarse) {
  .merchant-application {
    padding: 8px; /* 减少容器padding */
  }

  .application-header {
    padding: 20px 8px; /* 减少左右padding */
  }

  .application-header h3 {
    font-size: 18px; /* 稍微减小字体 */
    line-height: 1.3;
    margin-bottom: 8px;
  }

  .application-header p {
    font-size: 13px; /* 减小字体 */
    line-height: 1.4;
    word-wrap: break-word; /* 允许长单词换行 */
    overflow-wrap: break-word; /* 兼容性 */
  }

  .application-form {
    padding: 20px 16px;
  }



  .form-input,
  .form-textarea {
    padding: 10px 12px;
    font-size: 14px;
  }

  .submit-btn {
    padding: 12px 20px;
    font-size: 15px;
  }

  .requirement-notice {
    margin: 16px;
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .notice-content h4 {
    font-size: 15px;
  }

  .notice-content p {
    font-size: 13px;
  }

  /* 移动端图片上传样式 */
  .upload-button {
    min-width: 140px;
    padding: 10px 16px;
    font-size: 13px;
  }

  .preview-image {
    width: 100px;
    height: 100px;
  }

  .upload-hint {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .merchant-application {
    padding: 4px; /* 进一步减少容器padding */
  }

  .application-header {
    padding: 16px 12px;
  }

  .application-form {
    padding: 16px 12px;
  }



  .application-notice {
    padding: 12px;
  }

  .application-notice li {
    font-size: 13px;
  }
}

/* 注册信息展示 */
.registration-info {
  margin: 20px 0;
}

.flow-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.flow-step {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.flow-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.step-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 4px;
}

.step-content h5 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.step-content p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}



/* 状态样式 */
.status.registered {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status.pending-submit {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* 取消按钮样式 */
.cancel-button {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  border: 1px solid #dee2e6;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
}

.cancel-button:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cancel-button:active {
  transform: translateY(0);
}

/* 表单操作按钮布局 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

/* 全屏按钮样式 */
.form-actions.full-width {
  justify-content: center;
  padding: 24px;
  margin: 0;
  border-top: none;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 0 0 16px 16px;
}

.submit-button.primary-action {
  width: 100%;
  max-width: none; /* 移除最大宽度限制 */
  padding: 20px 32px;
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  box-shadow: 0 4px 14px rgba(16, 185, 129, 0.3);
}

/* 确保按钮占满全宽 */
.submit-button.primary-action.full-width-button {
  width: 100% !important;
  max-width: 100% !important; /* 确保不超过容器宽度 */
  flex: 1 !important;
  box-sizing: border-box !important;
  overflow: visible; /* 允许内容正常显示 */
  white-space: nowrap !important; /* 防止文本换行，保持在一行 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

.submit-button.primary-action:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.submit-button.primary-action:active:not(:disabled) {
  transform: translateY(0);
}

.submit-button.primary-action .button-text {
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.5px;
  display: inline-block !important; /* 确保文字显示 */
  visibility: visible !important; /* 确保可见 */
  opacity: 1 !important; /* 确保不透明 */
}

.submit-button.primary-action .submit-icon {
  font-size: 20px;
  display: inline-block !important; /* 确保图标显示 */
  visibility: visible !important; /* 确保可见 */
  opacity: 1 !important; /* 确保不透明 */
}

.submit-button.primary-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.submit-button.primary-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(16, 185, 129, 0.4);
}

.submit-button.primary-action:hover::before {
  left: 100%;
}

.submit-button.primary-action:active {
  transform: translateY(0);
}

.submit-button.primary-action.loading {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  cursor: not-allowed;
  box-shadow: 0 4px 20px rgba(107, 114, 128, 0.3);
}

.submit-button.primary-action .submit-icon {
  font-size: 24px;
  animation: bounce 2s infinite;
}

.submit-button.primary-action .button-text {
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.submit-button.primary-action .loading-spinner {
  font-size: 20px;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .form-actions.full-width {
    padding: 20px 8px; /* 减少左右padding，给按钮更多空间 */
  }

  .submit-button.primary-action {
    padding: 18px 16px; /* 减少左右padding */
    font-size: 16px;
    width: 100% !important;
    max-width: none !important;
    box-sizing: border-box; /* 确保padding不会导致溢出 */
  }

  .submit-button.primary-action .submit-icon {
    font-size: 20px;
  }

  .submit-button.primary-action .button-text {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .application-header {
    padding: 16px 4px; /* 进一步减少左右padding */
  }

  .application-header h3 {
    font-size: 16px; /* 进一步减小字体 */
    line-height: 1.2;
    margin-bottom: 6px;
  }

  .application-header p {
    font-size: 12px; /* 进一步减小字体 */
    line-height: 1.3;
  }

  .form-actions.full-width {
    padding: 16px 4px; /* 进一步减少左右padding */
  }

  .submit-button.primary-action {
    padding: 16px 12px; /* 减少左右padding */
    font-size: 15px;
    width: 100% !important;
    max-width: none !important;
    box-sizing: border-box;
    margin: 0; /* 确保没有外边距 */
  }

  .submit-button.primary-action .submit-icon {
    font-size: 18px;
  }

  .submit-button.primary-action .button-text {
    font-size: 15px;
  }

  /* 移动端按钮文本优化 */
  .submit-button.primary-action.full-width-button {
    white-space: normal !important; /* 允许文本换行 */
    min-height: 56px; /* 确保按钮有足够高度 */
    align-items: center;
    justify-content: center;
    display: flex !important; /* 确保flex布局 */
    flex-direction: row !important; /* 水平排列图标和文字 */
    gap: 8px !important; /* 图标和文字之间的间距 */
    white-space: nowrap !important; /* 防止文字换行 */
  }

  .submit-button.primary-action .button-text {
    text-align: center;
    line-height: 1.2;
    flex-shrink: 0; /* 防止文字被压缩 */
    display: inline-block; /* 确保文字正常显示 */
  }
}

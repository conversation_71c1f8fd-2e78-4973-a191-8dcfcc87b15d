// src/utils/rpcManager.js
/**
 * RPC 节点管理器 - 自动切换到可用的节点
 */

// BSC 测试网 RPC 节点列表（按优先级排序，移除经常超时的节点）
const BSC_TESTNET_RPCS = [
  'https://bsc-testnet.public.blastapi.io',
  'https://data-seed-prebsc-1-s1.binance.org:8545',
  'https://data-seed-prebsc-2-s1.binance.org:8545',
  'https://bsc-testnet.blockpi.network/v1/rpc/public',
  'https://data-seed-prebsc-1-s2.binance.org:8545',
  'https://data-seed-prebsc-2-s2.binance.org:8545',
  'https://data-seed-prebsc-1-s3.binance.org:8545',
  'https://data-seed-prebsc-2-s3.binance.org:8545',
  // 'https://bsc-testnet-rpc.publicnode.com', // 经常超时，暂时移除
];

// BSC 主网 RPC 节点列表
const BSC_MAINNET_RPCS = [
  'https://bsc-dataseed1.binance.org',
  'https://bsc-dataseed2.binance.org',
  'https://bsc-dataseed3.binance.org',
  'https://bsc-dataseed4.binance.org',
  'https://bsc-dataseed1.defibit.io',
  'https://bsc-dataseed2.defibit.io',
  'https://bsc-dataseed3.defibit.io',
  'https://bsc-dataseed4.defibit.io',
];

/**
 * 测试 RPC 节点是否可用
 * @param {string} rpcUrl - RPC 节点 URL
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<boolean>} - 是否可用
 */
async function testRpcNode(rpcUrl, timeout = 5000) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(rpcUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'eth_blockNumber',
        params: [],
        id: 1,
      }),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      return data.result && data.result.startsWith('0x');
    }
    return false;
  } catch (error) {
    // 只在非超时错误时输出警告
    if (!error.message.includes('aborted') && !error.message.includes('timeout')) {
      console.warn(`🔍 [RPC测试] ${rpcUrl} 不可用:`, error.message);
    }
    return false;
  }
}

/**
 * 获取最快的可用 RPC 节点
 * @param {number} chainId - 链 ID (97=测试网, 56=主网)
 * @returns {Promise<string>} - 可用的 RPC URL
 */
export async function getFastestRpc(chainId) {
  const rpcList = chainId === 97 ? BSC_TESTNET_RPCS : BSC_MAINNET_RPCS;
  const networkName = chainId === 97 ? 'BSC网络' : 'BSC主网';

  console.log(`🔍 [RPC管理器] 开始测试 ${networkName} RPC 节点...`);

  // 并发测试所有 RPC 节点，返回第一个可用的
  const testPromises = rpcList.map(async (rpc, index) => {
    const isAvailable = await testRpcNode(rpc, 3000);
    if (isAvailable) {
      console.log(`✅ [RPC管理器] 找到可用节点: ${rpc} (优先级: ${index + 1})`);
      return { rpc, priority: index };
    }
    return null;
  });

  try {
    // 等待第一个可用的 RPC 节点
    const results = await Promise.allSettled(testPromises);
    const availableRpcs = results
      .map(result => result.status === 'fulfilled' ? result.value : null)
      .filter(Boolean)
      .sort((a, b) => a.priority - b.priority); // 按优先级排序

    if (availableRpcs.length > 0) {
      const selectedRpc = availableRpcs[0].rpc;
      console.log(`🎯 [RPC管理器] 选择 RPC 节点: ${selectedRpc}`);
      return selectedRpc;
    }
  } catch (error) {
    console.error(`🚨 [RPC管理器] 测试 RPC 节点时出错:`, error);
  }

  // 如果所有节点都不可用，返回默认节点
  const fallbackRpc = rpcList[0];
  console.warn(`⚠️ [RPC管理器] 所有 RPC 节点都不可用，使用默认节点: ${fallbackRpc}`);
  return fallbackRpc;
}

/**
 * 获取环境变量中配置的 RPC 或自动选择最快的
 * @param {number} chainId - 链 ID
 * @returns {Promise<string>} - RPC URL
 */
export async function getRpcUrl(chainId) {
  // 优先使用环境变量配置的 RPC
  const envRpc = chainId === 97 
    ? import.meta.env.VITE_RPC_URL_TESTNET 
    : import.meta.env.VITE_RPC_URL_MAINNET;

  if (envRpc) {
    console.log(`🔧 [RPC管理器] 使用环境变量配置的 RPC: ${envRpc}`);
    
    // 测试环境变量配置的 RPC 是否可用
    const isAvailable = await testRpcNode(envRpc, 5000);
    if (isAvailable) {
      return envRpc;
    } else {
      console.warn(`⚠️ [RPC管理器] 环境变量 RPC 不可用，自动选择备用节点`);
    }
  }

  // 如果环境变量 RPC 不可用，自动选择最快的
  return await getFastestRpc(chainId);
}

/**
 * 创建带超时配置的 RPC 客户端
 * @param {number} chainId - 链 ID
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} - viem 客户端
 */
export async function createRpcClient(chainId = 97, options = {}) {
  const {
    timeout = 10000,
    retryCount = 3,
    retryDelay = 1000,
  } = options;

  const { createPublicClient, http } = await import('viem');
  const { bscTestnet } = await import('viem/chains');

  const rpcUrl = await getRpcUrl(chainId);

  return createPublicClient({
    chain: bscTestnet,
    transport: http(rpcUrl, {
      timeout,
      retryCount,
      retryDelay,
    })
  });
}

export default {
  getFastestRpc,
  getRpcUrl,
  testRpcNode,
  createRpcClient,
};

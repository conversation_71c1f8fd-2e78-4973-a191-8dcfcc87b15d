import React from 'react'
import { useAccount } from 'wagmi'
import { toast } from 'react-hot-toast'
import './index.css'

// 导入统一的 hooks 和组件
import { useQPTBuybackRooms } from '@/hooks/useQPTBuybackRooms'
import UnifiedRoomCard from '@/components/QPTBuyback/UnifiedRoomCard'
import { useQPTBuyback } from './hooks/useQPTBuyback'


export default function QPTBuyback() {
  const { address: account, isConnected } = useAccount()

  // 使用统一的 hook
  const {
    rooms,
    isLoading,
    isError,
    error,
    refreshRooms
  } = useQPTBuybackRooms(false) // false表示不是"我的房间"页面

  // 使用资金池数据 hook
  const {
    buybackData,
    isLoading: isLoadingBuyback,
    loadBuybackData
  } = useQPTBuyback()

  // 移除复杂的状态管理，改为简单的刷新机制

  // 状态管理
  const [isJoining, setIsJoining] = React.useState(false)
  const [isClaimingReward, setIsClaimingReward] = React.useState(false)
  const [isProcessingExpire, setIsProcessingExpire] = React.useState(false)


  // 分页状态
  const [currentPage, setCurrentPage] = React.useState(1)
  const roomsPerPage = 10

  // 分页计算
  const totalRooms = rooms.length
  const totalPages = Math.ceil(totalRooms / roomsPerPage)
  const startIndex = (currentPage - 1) * roomsPerPage
  const endIndex = startIndex + roomsPerPage
  const currentRooms = rooms.slice(startIndex, endIndex)



  // 计算统计数据
  const stats = React.useMemo(() => {
    const activeRooms = rooms.filter(room => room.status === 'active')
    const fullRooms = rooms.filter(room => room.status === 'full')
    const completedRooms = rooms.filter(room => room.status === 'completed')
    const expiredRooms = rooms.filter(room => room.status === 'expired')

    return {
      total: rooms.length,
      active: activeRooms.length,
      full: fullRooms.length,
      completed: completedRooms.length,
      expired: expiredRooms.length
    }
  }, [rooms])

  // 处理参与房间
  const handleJoinRoom = async (roomId) => {
    if (!account) {
      console.error('请先连接钱包')
      return
    }

    setIsJoining(true)
    try {
      // TODO: 实现参与房间逻辑
      console.log('参与房间:', roomId)
      // 参与成功后刷新数据
      await refreshRooms()
    } catch (error) {
      console.error('参与房间失败:', error)
    } finally {
      setIsJoining(false)
    }
  }

  // 处理领取奖励
  const handleClaimReward = async (room) => {
    setIsClaimingReward(true)
    console.log('🎯 [领取奖励] 开始处理房间:', room.id)

    try {
      // 1. 验证用户QPT锁仓状态
      console.log('🔍 [领取奖励] 步骤1: 验证QPT锁仓状态')
      const { verifyUserQPTLock } = await import('@/apis/qptBuybackApi')
      const verification = await verifyUserQPTLock({
        roomId: room.id,
        userAddress: account
      })
      console.log('🔍 [领取奖励] QPT锁仓验证结果:', verification)

      if (!verification.isValid) {
        throw new Error(`QPT锁仓验证失败: ${verification.message}`)
      }

      // 2. 查询退款状态
      console.log('🔍 [领取奖励] 步骤2: 查询退款状态')
      const { CONTRACT_ADDRESSES, ABIS } = await import('@/contracts')
      const { createPublicClient, http } = await import('viem')
      const { bscTestnet } = await import('viem/chains')

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      })

      const buybackAddress = CONTRACT_ADDRESSES[97].QPTBuyback
      console.log('🔍 [领取奖励] 合约地址:', buybackAddress)

      const [canRefund, refundType, reason] = await publicClient.readContract({
        address: buybackAddress,
        abi: ABIS.QPTBuyback,
        functionName: 'getRefundStatus',
        args: [room.id, account]
      })
      console.log('🔍 [领取奖励] 退款状态查询结果:', { canRefund, refundType, reason })

      if (!canRefund || refundType !== 2) {
        throw new Error(`无法领取本金+补贴: ${reason}`)
      }

      // 3. 执行领取操作
      console.log('🔍 [领取奖励] 步骤3: 执行领取操作')
      const { getWalletClient } = await import('wagmi/actions')
      const { config } = await import('@/wagmi.config')
      const walletClient = await getWalletClient(config)

      if (!walletClient) {
        throw new Error('无法获取钱包客户端')
      }
      console.log('🔍 [领取奖励] 钱包客户端获取成功')

      console.log('🔍 [领取奖励] 准备发送交易，参数:', {
        address: buybackAddress,
        functionName: 'refundSuccess',
        args: [BigInt(room.id)],
        account
      })

      const txHash = await walletClient.writeContract({
        address: buybackAddress,
        abi: ABIS.QPTBuyback,
        functionName: 'refundSuccess',
        args: [BigInt(room.id)],
        account
      })

      console.log('✅ [领取奖励] 交易已发送:', txHash)
      showSuccessMessage('领取本金+补贴交易已提交，等待确认...')

      // 等待交易确认
      console.log('🔍 [领取奖励] 步骤4: 等待交易确认')
      const receipt = await publicClient.waitForTransactionReceipt({
        hash: txHash,
        timeout: 60000
      })

      console.log('🔍 [领取奖励] 交易回执:', receipt)

      // 检查交易状态
      if (receipt.status === 'reverted') {
        console.error('❌ [领取奖励] 交易被回滚:', receipt)

        // 查询合约状态以分析失败原因
        try {
          console.log('🔍 [领取奖励] 分析失败原因，查询合约状态...')

          // 查询房间详细信息
          const roomInfo = await publicClient.readContract({
            address: buybackAddress,
            abi: ABIS.QPTBuyback,
            functionName: 'getRoomInfoWithTier',
            args: [room.id]
          })

          const [creator, participants, winner, lockedBuybackAmount, deadline, locked, tier, participantQPT, qptSubsidy] = roomInfo

          // 重新查询退款状态（包含是否已退款的信息）
          const [canRefund, refundType, reason] = await publicClient.readContract({
            address: buybackAddress,
            abi: ABIS.QPTBuyback,
            functionName: 'getRefundStatus',
            args: [room.id, account]
          })

          // 检查用户锁仓金额
          const userLockedAmount = await publicClient.readContract({
            address: buybackAddress,
            abi: ABIS.QPTBuyback,
            functionName: 'getUserRoomLocked',
            args: [account, room.id]
          })

          console.log('🔍 [领取奖励] 合约状态分析:', {
            roomId: room.id,
            creator,
            participantsCount: participants.length,
            winner,
            deadline: new Date(Number(deadline) * 1000),
            locked,
            tier,
            canRefund,
            refundType,
            reason,
            userLockedAmount: Number(userLockedAmount) / 1e18,
            currentTime: new Date(),
            isExpired: Date.now() > Number(deadline) * 1000,
            userAddress: account,
            isUserWinner: account.toLowerCase() === winner.toLowerCase()
          })

          // 重新查询最新的退款状态，因为状态可能在交易过程中发生了变化
          console.log('🔍 [领取奖励] 重新查询最新退款状态...')
          const [latestCanRefund, latestRefundType, latestReason] = await publicClient.readContract({
            address: buybackAddress,
            abi: ABIS.QPTBuyback,
            functionName: 'getRefundStatus',
            args: [room.id, account]
          })

          console.log('🔍 [领取奖励] 最新退款状态:', {
            latestCanRefund,
            latestRefundType,
            latestReason,
            previousReason: reason
          })

          // 尝试模拟合约调用来获取具体的错误信息
          try {
            console.log('🔍 [领取奖励] 尝试模拟合约调用...')
            await publicClient.simulateContract({
              address: buybackAddress,
              abi: ABIS.QPTBuyback,
              functionName: 'refundSuccess',
              args: [BigInt(room.id)],
              account
            })
            console.log('🔍 [领取奖励] 模拟调用成功，交易应该可以执行')
          } catch (simulateError) {
            console.error('🔍 [领取奖励] 模拟调用失败:', simulateError)
            console.error('🔍 [领取奖励] 模拟调用错误详情:', {
              message: simulateError.message,
              cause: simulateError.cause,
              data: simulateError.data,
              shortMessage: simulateError.shortMessage
            })

            // 基于模拟调用的具体错误提供准确的错误信息
            let simulateFailureReason = '交易执行失败，合约调用被回滚。'

            if (simulateError.shortMessage && simulateError.shortMessage.includes('Insufficient subsidy pool')) {
              // 查询补贴池余额信息
              try {
                const balanceInfo = await publicClient.readContract({
                  address: buybackAddress,
                  abi: ABIS.QPTBuyback,
                  functionName: 'getQPTBalanceInfo'
                })
                const [contractBalance, totalLocked, subsidyPool, available] = balanceInfo
                console.log('🔍 [补贴池信息]:', {
                  contractBalance: Number(contractBalance) / 1e18,
                  totalLocked: Number(totalLocked) / 1e18,
                  subsidyPool: Number(subsidyPool) / 1e18,
                  available: Number(available) / 1e18,
                  requiredSubsidy: 5 // QPT
                })
                simulateFailureReason += `原因：补贴池余额不足。当前补贴池：${(Number(subsidyPool) / 1e18).toFixed(2)} QPT，需要：5 QPT。管理员需要调用 depositSubsidy 函数充值补贴池。`
              } catch (balanceError) {
                console.error('查询补贴池余额失败:', balanceError)
                simulateFailureReason += '原因：补贴池余额不足，无法支付QPT补贴。管理员需要调用 depositSubsidy 函数充值补贴池。'
              }
            } else if (simulateError.shortMessage && simulateError.shortMessage.includes('Already refunded')) {
              simulateFailureReason += '原因：您已经领取过本金+补贴了。'
            } else if (simulateError.shortMessage && simulateError.shortMessage.includes('Not a participant')) {
              simulateFailureReason += '原因：您不是此房间的参与者。'
            } else if (simulateError.shortMessage && simulateError.shortMessage.includes('Winner cannot refund')) {
              simulateFailureReason += '原因：赢家不能领取本金+补贴，应该领取奖励。'
            } else {
              simulateFailureReason += `原因：${simulateError.shortMessage || '未知错误'}。请联系技术支持。`
            }

            throw new Error(simulateFailureReason)
          }

          // 分析具体失败原因 - 使用最新的退款状态
          let failureReason = '交易执行失败，合约调用被回滚。'

          // 使用最新查询的状态进行分析
          if (latestReason === "Already refunded") {
            failureReason += '原因：您已经领取过本金+补贴了。'
          } else if (latestReason === "Not a participant") {
            failureReason += '原因：您不是此房间的参与者。'
          } else if (latestReason === "Winner cannot refund") {
            failureReason += '原因：赢家不能领取本金+补贴，应该领取奖励。'
          } else if (latestReason === "Room expired but not processed yet") {
            failureReason += '原因：房间已过期但尚未处理，请等待管理员处理或联系技术支持。'
          } else if (latestReason === "Room full but not expired or finalized") {
            failureReason += '原因：房间已满员但未过期或未开奖。'
          } else if (latestReason === "Room still active") {
            failureReason += '原因：房间仍在进行中，请等待开奖或房间过期。'
          } else {
            // 对于其他情况，基于合约状态进行分析
            if (Number(userLockedAmount) === 0) {
              failureReason += '原因：您在此房间没有锁仓QPT。'
            } else if (participants.length < 8) {
              failureReason += '原因：房间未满员。'
            } else if (account.toLowerCase() === winner.toLowerCase()) {
              failureReason += '原因：您是赢家，不能领取本金+补贴，应该领取奖励。'
            } else if (latestCanRefund && latestRefundType === 2) {
              // 状态显示可以退款，但交易失败，可能是合约内部逻辑问题
              failureReason += '原因：合约内部验证失败。可能的原因包括：1) 您已经在其他交易中领取过；2) 合约状态在交易过程中发生变化；3) Gas费用不足。请刷新页面重试。'
            } else {
              failureReason += `原因：${latestReason || '未知错误'}。请刷新页面重试，如果问题持续，请联系技术支持。`
            }
          }

          throw new Error(failureReason)
        } catch (analysisError) {
          console.error('❌ [领取奖励] 状态分析失败:', analysisError)
          if (analysisError.message.includes('交易执行失败')) {
            throw analysisError // 重新抛出我们自己的错误
          }
          throw new Error('交易执行失败，合约调用被回滚。无法分析具体原因，请联系技术支持。')
        }
      }

      console.log('✅ [领取奖励] 交易确认成功:', receipt)
      console.log('✅ [领取奖励] 完整结果:', { roomId: room.id, txHash, verification, receipt })
      showSuccessMessage('🎉 成功领取本金+补贴！')

      // 刷新房间列表，按钮状态会自动更新
      await refreshRooms()
    } catch (error) {
      console.error('❌ [领取奖励] 失败:', error)
      console.error('❌ [领取奖励] 错误详情:', {
        message: error.message,
        code: error.code,
        data: error.data,
        stack: error.stack
      })

      // 根据错误类型提供更详细的错误信息
      let errorMessage = error.message
      if (error.code === 'ACTION_REJECTED') {
        errorMessage = '用户取消了交易'
      } else if (error.code === 'INSUFFICIENT_FUNDS') {
        errorMessage = '余额不足，无法支付Gas费用'
      } else if (error.message.includes('execution reverted')) {
        errorMessage = '合约执行失败，可能是条件不满足'
      }

      showErrorMessage(`领取奖励失败: ${errorMessage}`)
    } finally {
      setIsClaimingReward(false)
    }
  }

  // 处理赢家领取USDT奖励
  const handleClaimWinnerReward = async (room) => {
    setIsClaimingReward(true)

    try {
      const { CONTRACT_ADDRESSES, ABIS } = await import('@/contracts')
      const { getWalletClient } = await import('wagmi/actions')
      const { config } = await import('@/wagmi.config')
      const { createPublicClient, http } = await import('viem')
      const { bscTestnet } = await import('viem/chains')

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      })

      const buybackAddress = CONTRACT_ADDRESSES[97].QPTBuyback

      // 1. 检查赢家奖励状态
      const winnerRewardInfo = await publicClient.readContract({
        address: buybackAddress,
        abi: ABIS.QPTBuyback,
        functionName: 'getWinnerRewardInfo',
        args: [room.id, account]
      })

      const [canClaim, level, percent, rewardAmount, claimed, reason] = winnerRewardInfo

      if (claimed) {
        throw new Error('您已经领取过USDT奖励了')
      }

      if (!canClaim) {
        throw new Error('无法领取奖励，请检查您是否为赢家')
      }

      // 2. 执行领取操作
      const walletClient = await getWalletClient(config)
      if (!walletClient) {
        throw new Error('无法获取钱包客户端')
      }

      const txHash = await walletClient.writeContract({
        address: buybackAddress,
        abi: ABIS.QPTBuyback,
        functionName: 'claimReward',
        args: [BigInt(room.id)],
        account
      })

      showSuccessMessage('USDT奖励领取交易已提交，等待确认...')

      // 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({
        hash: txHash,
        timeout: 60000
      })

      if (receipt.status === 'reverted') {
        throw new Error('交易被回滚，领取失败')
      }

      showSuccessMessage('🎉 成功领取USDT奖励！')

      // 刷新房间列表，按钮状态会自动更新
      await refreshRooms()

      // 延迟一下确保房间数据完全刷新
      setTimeout(() => {
        // 触发额外的房间数据刷新，确保状态同步
        refreshRooms()
      }, 1500)

      // 刷新用户USDT余额
      try {
        const { refreshUserBalance } = await import('@/utils/dataRefreshManager')
        await refreshUserBalance(account)

        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          await window.refreshTokenBalances()
        }
      } catch (refreshError) {
        console.error('刷新余额失败:', refreshError)
      }

    } catch (error) {
      console.error('领取USDT奖励失败:', error)
      showErrorMessage(formatErrorMessage(error))
    } finally {
      setIsClaimingReward(false)
    }
  }

  // 处理过期房间
  const handleExpireRoom = async (roomId) => {
    setIsProcessingExpire(true)
    try {
      // TODO: 实现过期处理逻辑
      console.log('处理过期房间:', roomId)
      await refreshRooms()
    } catch (error) {
      console.error('处理过期房间失败:', error)
    } finally {
      setIsProcessingExpire(false)
    }
  }

  // 消息处理函数
  const showSuccessMessage = (message) => {
    toast.success(message)
  }

  const showErrorMessage = (message) => {
    toast.error(message)
  }

  const formatErrorMessage = (error) => {
    if (error?.message) {
      return error.message
    }
    return String(error)
  }

  if (!account || !isConnected) {
    return (
      <div className="qpt-buyback">
        <div className="connect-wallet-prompt">
          <h2>QPT 回购</h2>
          <p>请连接钱包以查看 QPT 回购信息</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="qpt-buyback">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>加载 QPT 回购数据中...</p>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="qpt-buyback">
        <div className="error-container">
          <h3>加载失败</h3>
          <p>无法加载QPT回购数据: {error?.message}</p>
          <button onClick={refreshRooms} className="retry-button">
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="qpt-buyback">
      {/* 模块头部 */}
      <div className="module-header">
        <div>
          <h3>🔄 QPT 回购</h3>
        </div>
        <button
          className="refresh-btn"
          onClick={() => {
            refreshRooms()
            loadBuybackData()
          }}
          disabled={isLoading || isLoadingBuyback}
        >
          {(isLoading || isLoadingBuyback) ? '刷新中...' : '刷新数据'}
        </button>
      </div>

      {/* 资金池信息 */}
      <div className="staking-info">
        <div className="info-grid">
          <div className="info-item">
            <div className="info-label">合约USDT余额</div>
            <div className="info-value">{parseFloat(buybackData.contractUSDTBalance || '0').toFixed(2)} USDT</div>
          </div>
          <div className="info-item">
            <div className="info-label">可用回购池</div>
            <div className="info-value">{parseFloat(buybackData.availableBuybackPool || '0').toFixed(2)} USDT</div>
          </div>
          <div className="info-item">
            <div className="info-label">待领取奖励</div>
            <div className="info-value">{parseFloat(buybackData.pendingRewards || '0').toFixed(2)} USDT</div>
          </div>
        </div>
      </div>

      {/* 房间统计信息 */}
      <div className="staking-info">
        <div className="info-grid">
          <div className="info-item">
            <div className="info-label">房间总数</div>
            <div className="info-value">{stats.total}</div>
          </div>
          <div className="info-item">
            <div className="info-label">进行中</div>
            <div className="info-value">{stats.active}</div>
          </div>
          <div className="info-item">
            <div className="info-label">满员待开奖</div>
            <div className="info-value">{stats.full}</div>
          </div>
          <div className="info-item">
            <div className="info-label">已完成</div>
            <div className="info-value">{stats.completed}</div>
          </div>
        </div>
      </div>

      {/* 活跃房间 */}
      <div className="active-rooms">
        <div className="rooms-header">
          <h4>🎯 活跃房间</h4>
          <div className="pagination-info">
            共 {totalRooms} 个房间
          </div>
        </div>

        <div className="rooms-grid">
          {currentRooms.length > 0 ? (
            currentRooms.map(room => (
              <UnifiedRoomCard
                key={room.id}
                room={room}
                account={account}
                onJoinRoom={handleJoinRoom}
                onClaimReward={handleClaimReward}
                onClaimWinnerReward={handleClaimWinnerReward}
                onExpireRoom={handleExpireRoom}
                onRefreshRooms={refreshRooms}
                isClaimingReward={isClaimingReward}
                isProcessingExpire={isProcessingExpire}
                showSuccessMessage={showSuccessMessage}
                showErrorMessage={showErrorMessage}
                formatErrorMessage={formatErrorMessage}
              />
            ))
          ) : (
            <div className="empty-state">
              <div className="empty-icon">💎</div>
              <h3>暂无QPT回购房间</h3>
              <p>目前没有活跃的QPT回购房间</p>
            </div>
          )}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="pagination">
            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              上一页
            </button>

            <div className="pagination-numbers">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                return (
                  <button
                    key={pageNum}
                    className={`pagination-number ${currentPage === pageNum ? 'active' : ''}`}
                    onClick={() => setCurrentPage(pageNum)}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              下一页
            </button>
          </div>
        )}
      </div>

      {/* 规则说明 */}
      <div className="rules-section">
        <h4>📋 回购规则</h4>
        <div className="rules-list">
          <div className="rule-item">• 参与回购需要锁定对应档位的QPT代币</div>
          <div className="rule-item">• 房间满员后等待发起人开奖，开奖结果将由区块链交易哈希随机决定</div>
          <div className="rule-item">• 赢家按代理等级获得回购池5%-25%USDT奖励，锁定的QPT被回购</div>
          <div className="rule-item">• 未中奖参与者可退还QPT本金+补贴</div>
          <div className="rule-item">• 房间24小时内未满员将自动过期，参与者可申请退款</div>
        </div>
      </div>
    </div>
  )
}
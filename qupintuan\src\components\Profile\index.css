/* Profile 个人中心通用样式 */

/* 用户信息卡片样式 */
.user-info-block {
  display: grid;
  gap: 16px;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.info-row:nth-child(1) { animation-delay: 0.1s; }
.info-row:nth-child(2) { animation-delay: 0.2s; }
.info-row:nth-child(3) { animation-delay: 0.3s; }
.info-row:nth-child(4) { animation-delay: 0.4s; }
.info-row:nth-child(5) { animation-delay: 0.5s; }
.info-row:nth-child(6) { animation-delay: 0.6s; }
.info-row:nth-child(7) { animation-delay: 0.7s; }
.info-row:nth-child(8) { animation-delay: 0.8s; }

.info-row:hover {
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  border-color: #c7d2fe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-row .label {
  font-weight: 600;
  color: #374151;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-row .value {
  font-weight: 500;
  color: #1f2937;
  font-size: 15px;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

/* 状态样式 */
.value.registered {
  color: #059669;
  background: #d1fae5;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
}

.value.not-registered {
  color: #dc2626;
  background: #fee2e2;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
}

.value.not-connected {
  color: #7c2d12;
  background: #fed7aa;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
}

/* 特殊数值样式 */
.value.performance-value {
  color: #059669;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid #10b981;
}

.value.team-count {
  color: #7c3aed;
  font-weight: 700;
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid #8b5cf6;
}

.value.level-badge {
  color: #dc2626;
  font-weight: 700;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid #ef4444;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 提示信息样式 */
.info-tip {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
  color: #92400e;
  font-style: italic;
  justify-content: center;
  text-align: center;
  padding: 20px;
  border-left: 4px solid #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.info-tip::before {
  content: '💡';
  margin-right: 8px;
}

/* 特殊信息行样式 */
.info-row:has(.value.registered) {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.info-row:has(.value.not-registered) {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.info-row:has(.value.not-connected) {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 16px;
  }

  .info-row .value {
    max-width: 100%;
    text-align: left;
  }


}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .info-row {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-color: #6b7280;
  }

  .info-row:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
    border-color: #8b5cf6;
  }

  .info-row .label {
    color: #f3f4f6;
  }

  .info-row .value {
    color: #e5e7eb;
  }

  .info-tip {
    background: linear-gradient(135deg, #92400e 0%, #b45309 100%);
    border-color: #f59e0b;
    color: #fef3c7;
  }
}

/* 直推用户列表样式 */
.referrals-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.section-header .label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.referrals-count {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 12px;
}

.loading-message, .empty-message {
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
}

.referrals-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.referral-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.referral-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.referral-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.referral-index {
  background: #3b82f6;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 24px;
  text-align: center;
}

.referral-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #374151;
  font-weight: 500;
  flex: 1;
}

.referral-status {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
}

.referral-status.active {
  background: #d1fae5;
  color: #065f46;
}

.referral-status.inactive {
  background: #fef3c7;
  color: #92400e;
}

.referral-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 12px;
  color: #374151;
  font-weight: 600;
}

/* 团队总人数样式 */
.team-total-count {
  color: #7c3aed;
  font-weight: 700;
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #c4b5fd;
  display: flex;
  align-items: center;
  gap: 8px;
}

.team-stats-note {
  font-size: 11px;
  color: #6b7280;
  font-weight: 400;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 推荐奖励标题和QPT统计横向布局 - 增强优先级 */
.referral-rewards-container .rewards-header-section {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 24px !important;
  gap: 16px !important;
  min-height: 60px !important;
  width: 100% !important;
}

.referral-rewards-container .rewards-header-section .profile-title {
  margin: 0 !important;
  padding: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
  flex: 1 !important;
  font-size: 24px !important;
  font-weight: 700 !important;
  color: #1f2937 !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  white-space: nowrap !important;
}

.referral-rewards-container .rewards-header-section .profile-title::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  flex-shrink: 0;
}

/* 额外的优先级保证 - 覆盖通用profile-title样式 */
.referral-rewards-container h2.profile-title {
  margin: 0 !important;
  padding: 0 !important;
  border-bottom: none !important;
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

/* QPT统计区域样式 */
.rewards-stats-section {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.total-rewards-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 12px 16px;
  text-align: center;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  z-index: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  min-width: 120px;
  height: 60px;
  flex-shrink: 0;
}

.rewards-label {
  font-size: 10px;
  opacity: 0.9;
  font-weight: 500;
  margin: 0;
  color: white;
  line-height: 1.2;
}

.rewards-value {
  font-size: 18px;
  font-weight: 900;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  margin: 0;
  color: white;
  line-height: 1.2;
}

/* 推荐奖励领取按钮样式 */
.claim-reward-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 200px;
  margin-top: 16px;
  width: 100%;
}

.claim-reward-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.claim-reward-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.claim-reward-btn.claiming {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  cursor: not-allowed;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.claim-reward-btn:disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  box-shadow: none;
}

/* 奖励项目样式调整 */
.reward-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 推荐奖励中的房间ID样式 - 与拼团房间保持一致 */
.room-id {
  font-size: 12px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  padding: 3px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  display: inline-block;
}

.room-id:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

/* 消息提示样式 */
.message-toast {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  animation: slideIn 0.3s ease-out;
}

.success-toast {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.error-toast {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 推荐奖励列表样式 */
.rewards-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

.reward-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.reward-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #c7d2fe;
}

.reward-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.reward-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.reward-info {
  flex: 1;
}

.room-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.room-id {
  font-size: 14px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  padding: 4px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.tier-amount {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
}

.tier-amount.pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #f59e0b;
}

.tier-amount.claimed {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.winner-info {
  margin-bottom: 16px;
}

.winner-address {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  color: #1d4ed8;
  background: #f0f4ff;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #c7d2fe;
  margin-bottom: 8px;
}

.reward-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.detail-item {
  text-align: center;
}

.detail-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.detail-value {
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
}

.detail-value.qpt-amount {
  color: #059669;
}

.detail-value.points-amount {
  color: #7c3aed;
}

.detail-value.status-claimed {
  color: #059669;
  font-weight: 600;
}

.detail-value.status-pending {
  color: #f59e0b;
  font-weight: 600;
}

.detail-value.status-unknown {
  color: #6b7280;
}

/* 已领取标识 */
.claimed-badge {
  font-size: 12px;
  color: #059669;
  background: #d1fae5;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
  margin-top: 4px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
  margin: 32px 0;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state h4 {
  font-size: 20px;
  color: #374151;
  margin-bottom: 12px;
  font-weight: 600;
}

.empty-state p {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 8px;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  margin: 32px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #6b7280;
  font-size: 16px;
}

/* 奖励说明样式 */
.rewards-note {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 16px;
  padding: 24px;
  margin-top: 32px;
}

.rewards-note h4 {
  color: #0c4a6e;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rewards-note ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rewards-note li {
  color: #0f172a;
  line-height: 1.6;
  margin-bottom: 12px;
  padding-left: 20px;
  position: relative;
}

.rewards-note li::before {
  content: '✨';
  position: absolute;
  left: 0;
  top: 0;
}

/* 分页组件样式 */
.pagination-container {
  margin-top: 32px;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.pagination-info {
  color: #64748b;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.pagination-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-btn {
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
  border-color: #e5e7eb;
}

.pagination-btn.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-color: #10b981;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.pagination-btn.active:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.pagination-btn.prev,
.pagination-btn.next {
  padding: 8px 16px;
  font-weight: 600;
}

.pagination-btn.number {
  min-width: 40px;
  height: 40px;
  padding: 0;
}

/* 分页响应式设计 */
@media (max-width: 768px) {
  .pagination-container {
    margin-top: 24px;
    padding: 16px;
    gap: 12px;
  }

  .pagination-controls {
    gap: 4px;
  }

  .pagination-btn {
    padding: 6px 8px;
    font-size: 13px;
    min-width: 36px;
  }

  .pagination-btn.prev,
  .pagination-btn.next {
    padding: 6px 12px;
  }

  .pagination-btn.number {
    min-width: 36px;
    height: 36px;
  }

  .pagination-numbers {
    gap: 2px;
  }

  .pagination-info {
    font-size: 13px;
  }
}

/* 推荐奖励页面响应式设计 */
@media (max-width: 768px) {
  .referral-rewards-container .rewards-header-section {
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: 12px;
    min-height: auto;
  }

  .referral-rewards-container .rewards-header-section .profile-title {
    align-self: stretch;
    margin: 0 !important;
    padding-bottom: 0 !important;
    border-bottom: none !important;
    font-size: 20px;
  }

  .total-rewards-card {
    padding: 10px 14px;
    min-width: 100px;
    height: 50px;
    align-self: flex-end;
  }

  .rewards-value {
    font-size: 16px;
  }

  .rewards-label {
    font-size: 9px;
  }

  .reward-item {
    padding: 20px;
  }

  .reward-main {
    flex-direction: column;
    gap: 16px;
  }

  .reward-details {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    padding: 12px;
  }

  .detail-value {
    font-size: 14px;
  }

  .empty-state {
    padding: 40px 16px;
  }

  .empty-icon {
    font-size: 48px;
  }

  .empty-state h4 {
    font-size: 18px;
  }

  .rewards-note {
    padding: 20px;
  }

  .rewards-note h4 {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .referral-rewards-container .rewards-header-section {
    gap: 8px;
  }

  .referral-rewards-container .rewards-header-section .profile-title {
    font-size: 18px;
  }

  .total-rewards-card {
    padding: 8px 12px;
    min-width: 90px;
    height: 45px;
  }

  .rewards-value {
    font-size: 14px;
  }

  .rewards-label {
    font-size: 8px;
  }

  .reward-item {
    padding: 16px;
  }

  .reward-details {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 12px;
  }

  .room-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .winner-address {
    font-size: 12px;
    padding: 6px 8px;
  }

  .empty-state {
    padding: 32px 12px;
  }

  .empty-icon {
    font-size: 40px;
  }

  .empty-state h4 {
    font-size: 16px;
  }

  .empty-state p {
    font-size: 14px;
  }

  .rewards-note {
    padding: 16px;
  }

  .rewards-note h4 {
    font-size: 15px;
  }

  .rewards-note li {
    font-size: 14px;
    padding-left: 16px;
  }

  .pagination-container {
    padding: 12px;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 8px;
  }

  .pagination-numbers {
    order: 2;
  }

  .pagination-btn.prev {
    order: 1;
  }

  .pagination-btn.next {
    order: 3;
  }
}

/* src/pages/Profile/index.css */

/* 标签页样式 */
.profile-tabs {
  margin: 24px 0;
  border-bottom: 2px solid #f3f4f6;
  display: flex;
  justify-content: center;
  gap: 4px;
  overflow-x: auto;
  padding-bottom: 2px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.2s;
  white-space: nowrap;
  position: relative;
}

.tab-button:hover {
  background: #f9fafb;
  color: #374151;
}

.tab-button.active {
  background: white;
  color: #10b981;
  border-bottom: 2px solid #10b981;
  margin-bottom: -2px;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #10b981;
}

.tab-btn:hover {
  background: #f9fafb;
  color: #374151;
}

.tab-btn.active {
  background: white;
  color: #10b981;
  border-bottom: 2px solid #10b981;
  margin-bottom: -2px;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #10b981;
}

.tab-icon {
  font-size: 18px;
}

.tab-label {
  font-weight: 500;
}

.profile-content {
  padding: 24px 0;
}

.tab-content {
  min-height: 400px;
}

.wallet-prompt {
  text-align: center;
  padding: 60px 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

.wallet-prompt h3 {
  color: #374151;
  margin-bottom: 8px;
}

.wallet-prompt p {
  color: #6b7280;
  margin: 0;
}

/* 标签按钮颜色主题 */
.tab-btn[data-tab="profile"] {
  --tab-color: #3b82f6;
  --tab-bg: #dbeafe;
  --tab-hover-bg: #bfdbfe;
}

.tab-btn[data-tab="rewards"] {
  --tab-color: #10b981;
  --tab-bg: #d1fae5;
  --tab-hover-bg: #a7f3d0;
}

.tab-btn[data-tab="merchant"] {
  --tab-color: #f59e0b;
  --tab-bg: #fef3c7;
  --tab-hover-bg: #fde68a;
}

.tab-btn[data-tab="settings"] {
  --tab-color: #8b5cf6;
  --tab-bg: #ede9fe;
  --tab-hover-bg: #ddd6fe;
}

.tab-btn:hover {
  background: var(--tab-hover-bg, #f9fafb);
  color: var(--tab-color, #374151);
}

.tab-btn.active {
  background: var(--tab-bg, white);
  color: var(--tab-color, #10b981);
  border-bottom: 2px solid var(--tab-color, #10b981);
  margin-bottom: -2px;
}

.tab-btn.active::after {
  background: var(--tab-color, #10b981);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-nav {
    flex-direction: column;
    gap: 8px;
    padding-bottom: 0;
  }

  .tab-btn {
    padding: 16px 20px;
    font-size: 16px;
    border-radius: 12px;
    border: 2px solid transparent;
    justify-content: flex-start;
    width: 100%;
    text-align: left;
  }

  .tab-btn:hover {
    border-color: var(--tab-color, #10b981);
    transform: translateX(4px);
  }

  .tab-btn.active {
    background: var(--tab-bg, white);
    border-color: var(--tab-color, #10b981);
    border-bottom: 2px solid var(--tab-color, #10b981);
    margin-bottom: 0;
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .tab-btn.active::after {
    display: none;
  }

  .tab-icon {
    font-size: 20px;
  }

  .tab-label {
    font-weight: 600;
  }

  .profile-content {
    padding: 20px 0;
  }

  .profile-tabs {
    margin: 20px 0;
    border-bottom: none;
  }
}

.profile-home {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #f8fafc 100%);
  padding: 20px;
}

.profile-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.profile-header h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-header p {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
}

.wallet-status {
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
}

.wallet-connected,
.wallet-disconnected {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 500;
}

.wallet-connected {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border: 1px solid #10b981;
}

.wallet-disconnected {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 1px solid #ef4444;
}

.status-indicator {
  font-size: 12px;
}

.profile-modules {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.profile-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 0;
}

.profile-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 24px 0;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f3f4f6;
}

.profile-title::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* 标题和操作按钮的横向布局 */
.section-title-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title-with-action .profile-title {
  margin: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.section-title-with-action .refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.section-title-with-action .refresh-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.section-title-with-action .refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.module-section {
  background: transparent;
}

.coming-soon-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.coming-soon-card h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #374151;
}

.coming-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.feature-item {
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.feature-item:hover {
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  border-color: #c7d2fe;
  color: #4338ca;
  transform: translateY(-1px);
}



/* 移动端标签页折叠菜单 */
.mobile-tab-selector {
  display: none;
  position: relative;
  width: calc(100% + 32px); /* 超出父容器宽度 */
  margin: 0 -16px; /* 抵消父容器的padding，使其满屏 */
  padding: 0 16px; /* 重新添加内边距 */
}

.mobile-tab-toggle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.mobile-tab-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.mobile-tab-toggle:hover::before {
  left: 100%;
}

.mobile-tab-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.mobile-tab-toggle:active {
  transform: translateY(0);
}

.toggle-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.current-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-hint {
  font-size: 12px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-arrow {
  transition: transform 0.3s ease;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-arrow.open {
  transform: rotate(180deg);
}

.mobile-tab-menu {
  position: absolute;
  top: calc(100% + 8px);
  left: 16px;
  right: 16px;
  background: white;
  border: none;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
  backdrop-filter: blur(10px);
}

.menu-header {
  padding: 16px 20px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  text-align: center;
}

.menu-title {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.menu-subtitle {
  display: block;
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.mobile-tab-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 20px;
  background: transparent;
  border: none;
  border-bottom: 1px solid #f1f5f9;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.mobile-tab-item .tab-icon {
  font-size: 18px;
  margin-right: 12px;
}

.mobile-tab-item .tab-text {
  flex: 1;
  font-weight: 500;
}

.mobile-tab-item .active-indicator {
  font-size: 16px;
  color: #10b981;
  font-weight: bold;
}

.mobile-tab-item:last-child {
  border-bottom: none;
}

.mobile-tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s;
}

.mobile-tab-item:hover::before {
  left: 100%;
}

.mobile-tab-item:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  color: #0369a1;
  transform: translateX(4px);
}

.mobile-tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.mobile-tab-item.active::before {
  display: none;
}

.menu-footer {
  padding: 12px 20px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
}

.close-menu-btn {
  width: 100%;
  padding: 10px 16px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-menu-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.desktop-tabs {
  display: flex;
  justify-content: center;
  gap: 4px;
  overflow-x: auto;
  padding-bottom: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-home {
    padding: 16px;
  }

  .profile-header {
    padding: 20px 16px;
    margin-bottom: 24px;
  }

  .profile-header h1 {
    font-size: 28px;
  }

  .profile-header p {
    font-size: 15px;
  }

  .wallet-status {
    margin-bottom: 24px;
  }

  .wallet-connected,
  .wallet-disconnected {
    padding: 10px 16px;
    font-size: 14px;
  }

  /* 移动端显示折叠菜单，隐藏桌面端标签页 */
  .mobile-tab-selector {
    display: block;
  }

  .desktop-tabs {
    display: none;
  }



  .profile-modules {
    gap: 20px;
  }

  .coming-soon-card {
    padding: 20px 16px;
  }

  .coming-soon-card h3 {
    font-size: 18px;
  }

  .coming-features {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .feature-item {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .profile-home {
    padding: 12px;
  }

  .profile-header {
    padding: 16px 12px;
    margin-bottom: 20px;
  }

  .profile-header h1 {
    font-size: 24px;
  }

  .profile-header p {
    font-size: 14px;
  }

  .wallet-connected,
  .wallet-disconnected {
    padding: 8px 12px;
    font-size: 13px;
    flex-direction: column;
    text-align: center;
    gap: 4px;
  }

  .coming-soon-card {
    padding: 16px 12px;
  }

  .coming-soon-card h3 {
    font-size: 16px;
    margin-bottom: 16px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-header,
.wallet-status,
.module-section {
  animation: fadeInUp 0.6s ease-out;
}

.module-section:nth-child(2) {
  animation-delay: 0.1s;
}

.module-section:nth-child(3) {
  animation-delay: 0.2s;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .profile-home {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }

  .profile-header,
  .coming-soon-card {
    background: #1e293b;
    border-color: #475569;
    color: #f1f5f9;
  }

  .profile-header p {
    color: #94a3b8;
  }

  .feature-item {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    border-color: #64748b;
    color: #cbd5e1;
  }

  .feature-item:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
    border-color: #8b5cf6;
    color: white;
  }
}

/* 商家管理后台卡片样式 */
.merchant-dashboard-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
}

.merchant-dashboard-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.merchant-dashboard-card h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
}

.merchant-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.merchant-badge.verified {
  background: #d1fae5;
  color: #065f46;
}

.merchant-badge .badge-icon {
  font-size: 14px;
}

.merchant-dashboard-card .card-content p {
  margin: 0 0 16px 0;
  color: #6b7280;
  line-height: 1.5;
}

.merchant-dashboard-card .feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.merchant-dashboard-card .feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.merchant-dashboard-card .feature-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.merchant-dashboard-card .feature-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.dashboard-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.dashboard-btn.primary {
  background: #10b981;
  color: white;
}

.dashboard-btn.primary:hover {
  background: #059669;
}

.dashboard-btn.secondary {
  background: #6b7280;
  color: white;
}

.dashboard-btn.secondary:hover {
  background: #4b5563;
}

.dashboard-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 商家状态卡片样式 */
.merchant-status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.merchant-status-card.pending {
  border-left: 4px solid #f59e0b;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.status-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.status-content p {
  margin: 0 0 16px 0;
  color: #6b7280;
  line-height: 1.5;
}

.status-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.tip-item {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.status-actions {
  display: flex;
  gap: 12px;
}

.status-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f59e0b;
  color: white;
}

.status-btn:hover:not(:disabled) {
  background: #d97706;
}

.status-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 调试信息样式 */
.debug-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}

.debug-label {
  font-weight: 600;
}

.debug-value {
  font-family: monospace;
  font-size: 11px;
}

/* 钱包连接状态调整 */
.wallet-connected {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.wallet-connected > span:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 注册区域样式 */
.section-subtitle {
  color: #374151;
  margin: 32px 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f3f4f6;
}

.section-subtitle::before {
  content: "🎯";
  font-size: 24px;
}

.registration-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

/* 优化注册组件在个人中心的显示 */
.profile-section .user-registration {
  margin: 0;
  padding: 0;
}

.profile-section .registration-card {
  margin-top: 16px;
}



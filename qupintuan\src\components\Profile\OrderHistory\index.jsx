// src/components/Profile/OrderHistory/index.jsx
// 用户订单历史组件

import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import {
  getBuyerOrders,
  ORDER_STATUS,
  ORDER_STATUS_TEXT,
  ORDER_STATUS_COLOR
} from '@/apis/orderApi';
import { getProduct } from '@/apis/mallApi';
import { formatAddress } from '@/utils/addressFormatter';
import './OrderHistory.css';

const OrderHistory = forwardRef((props, ref) => {
  const { address: account, isConnected } = useAccount();
  
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalOrders, setTotalOrders] = useState(0);

  // 加载订单列表
  const loadOrders = async () => {
    if (!account || !isConnected) return;

    setIsLoading(true);
    try {
      const orderList = await getBuyerOrders({ buyerAddress: account });

      // 为每个订单获取商品信息
      const ordersWithProducts = await Promise.all(
        orderList.map(async (order) => {
          try {
            const product = await getProduct({ productId: order.productId });
            return { ...order, product };
          } catch (error) {
            console.error('获取商品信息失败:', error);
            return { ...order, product: null };
          }
        })
      );

      setOrders(ordersWithProducts);
      setTotalOrders(ordersWithProducts.length);

      // 应用当前过滤器
      applyFilter(ordersWithProducts, statusFilter);

    } catch (error) {
      toast.error('加载订单列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 应用过滤器（内部函数）
  const applyFilter = (orderList, status) => {
    let filtered;
    if (status === 'all') {
      filtered = orderList;
    } else {
      filtered = orderList.filter(order => order.status === parseInt(status));
    }
    setFilteredOrders(filtered);
    setTotalOrders(filtered.length);
    setCurrentPage(1); // 重置到第一页
  };

  // 过滤订单（外部调用）
  const filterOrders = (status) => {
    setStatusFilter(status);
    applyFilter(orders, status);
  };

  // 分页控制函数
  const totalPages = Math.ceil(totalOrders / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // 复制快递单号
  const copyTrackingNumber = async (trackingNumber) => {
    try {
      await navigator.clipboard.writeText(trackingNumber);
      toast.success('快递单号已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      toast.error('复制失败，请手动复制');
    }
  };

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp || timestamp === 0) return '-';
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  // 格式化价格
  const formatPrice = (price) => {
    return (price / 1000000).toFixed(2);
  };

  // 暴露loadOrders函数给父组件
  useImperativeHandle(ref, () => ({
    loadOrders
  }));

  // 组件挂载时加载数据
  useEffect(() => {
    loadOrders();
  }, [account, isConnected]);

  if (!account || !isConnected) {
    return (
      <div className="order-history">
        <div className="not-connected">
          <h3>请先连接钱包</h3>
          <p>连接钱包后即可查看订单历史</p>
        </div>
      </div>
    );
  }

  return (
    <div className="order-history">

      {/* 状态过滤器 */}
      <div className="status-filters">
        <button
          className={`filter-btn ${statusFilter === 'all' ? 'active' : ''}`}
          onClick={() => filterOrders('all')}
        >
          全部订单
        </button>
        <button
          className={`filter-btn ${statusFilter === ORDER_STATUS.PENDING.toString() ? 'active' : ''}`}
          onClick={() => filterOrders(ORDER_STATUS.PENDING.toString())}
        >
          待发货
        </button>
        <button
          className={`filter-btn ${statusFilter === ORDER_STATUS.SHIPPED.toString() ? 'active' : ''}`}
          onClick={() => filterOrders(ORDER_STATUS.SHIPPED.toString())}
        >
          已发货
        </button>
      </div>

      {/* 订单列表 */}
      <div className="orders-content">
        {isLoading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>正在加载订单...</p>
          </div>
        ) : filteredOrders.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">🛒</div>
            <h4>暂无订单</h4>
            <p>您还没有{statusFilter === 'all' ? '' : ORDER_STATUS_TEXT[parseInt(statusFilter)] || ''}订单</p>
          </div>
        ) : (
          <div className="orders-list">
            {paginatedOrders.map((order) => (
              <div key={order.orderId} className="order-card">
                <div className="order-header">
                  <div className="order-info">
                    <span className="order-id">订单号: #{order.orderId}</span>
                    <span 
                      className="order-status"
                      style={{ color: ORDER_STATUS_COLOR[order.status] }}
                    >
                      {ORDER_STATUS_TEXT[order.status]}
                    </span>
                  </div>
                  <div className="order-time">
                    下单时间: {formatTime(order.createTime)}
                  </div>
                </div>

                <div className="order-content">
                  <div className="product-info">
                    <h4>{order.product?.name || '商品信息加载失败'}</h4>
                    <div className="price-info">
                      <span>数量: {order.quantity}</span>
                      <span>单价: {formatPrice(order.totalPrice / order.quantity)} 积分</span>
                      <span className="total-price">总价: {formatPrice(order.totalPrice)} 积分</span>
                    </div>
                  </div>

                  <div className="merchant-info">
                    <p><strong>商家:</strong> {formatAddress(order.merchant)}</p>
                    <p><strong>地址ID:</strong> {order.addressId}</p>
                  </div>

                  {order.status >= ORDER_STATUS.SHIPPED && (
                    <div className="shipping-info">
                      <div className="shipping-header">
                        <h5>📦 物流信息</h5>
                      </div>
                      <div className="shipping-details">
                        <p><strong>快递公司:</strong> {order.shippingCompany}</p>
                        <div className="tracking-number">
                          <span><strong>快递单号:</strong> {order.trackingNumber}</span>
                          <button 
                            className="copy-btn"
                            onClick={() => copyTrackingNumber(order.trackingNumber)}
                            title="复制快递单号"
                          >
                            📋 复制
                          </button>
                        </div>
                        <p><strong>发货时间:</strong> {formatTime(order.shippedTime)}</p>
                        {order.deliveredTime > 0 && (
                          <p><strong>送达时间:</strong> {formatTime(order.deliveredTime)}</p>
                        )}
                      </div>
                      <div className="tracking-tip">
                        💡 复制快递单号后，可前往对应快递公司官网或小程序查询物流详情
                      </div>
                    </div>
                  )}
                </div>

                <div className="order-timeline">
                  <div className={`timeline-item ${order.createTime > 0 ? 'completed' : ''}`}>
                    <div className="timeline-dot"></div>
                    <div className="timeline-content">
                      <span className="timeline-title">订单创建</span>
                      <span className="timeline-time">{formatTime(order.createTime)}</span>
                    </div>
                  </div>
                  
                  <div className={`timeline-item ${order.status >= ORDER_STATUS.SHIPPED ? 'completed' : ''}`}>
                    <div className="timeline-dot"></div>
                    <div className="timeline-content">
                      <span className="timeline-title">商家发货</span>
                      <span className="timeline-time">{formatTime(order.shippedTime)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 分页组件 */}
        {!isLoading && totalOrders > itemsPerPage && (
          <div className="pagination-container">
            <div className="pagination-info">
              显示第 {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalOrders)} 项，共 {totalOrders} 项
            </div>
            <div className="pagination-controls">
              <button
                className="pagination-btn"
                onClick={handlePrevPage}
                disabled={currentPage === 1}
              >
                ← 上一页
              </button>

              {/* 页码按钮 */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  className={`pagination-btn ${page === currentPage ? 'active' : ''}`}
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </button>
              ))}

              <button
                className="pagination-btn"
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
              >
                下一页 →
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

export default OrderHistory;

// 测试 RPC 超时修复
import { getUserPersonalPerformance } from '../apis/agentSystemApi.js';
import { createRpcClient, getFastestRpc } from '../utils/rpcManager.js';

/**
 * 测试 RPC 超时处理
 */
async function testRpcTimeout() {
  console.log('🧪 开始测试 RPC 超时处理...');
  
  // 测试用户地址
  const testAddresses = [
    '0xe981b3e562979ad22f1c8a911da39356998a181e',
    '0xe44b26710c9717ddbcfb7d90f3192ef44c174d29',
    '0xa2dd965e6aae7bea5cd24cfc05653b8c72c2f9ef'
  ];

  for (const userAddress of testAddresses) {
    console.log(`\n📊 测试用户: ${userAddress}`);
    
    try {
      const startTime = Date.now();
      const performance = await getUserPersonalPerformance({ userAddress });
      const endTime = Date.now();
      
      console.log(`✅ 获取成功: ${performance} (耗时: ${endTime - startTime}ms)`);
    } catch (error) {
      console.error(`❌ 获取失败:`, error.message);
    }
  }
}

/**
 * 测试 RPC 管理器
 */
async function testRpcManager() {
  console.log('\n🔧 测试 RPC 管理器...');
  
  try {
    const startTime = Date.now();
    const rpcUrl = await getFastestRpc(97);
    const endTime = Date.now();
    
    console.log(`✅ 获取最快 RPC: ${rpcUrl} (耗时: ${endTime - startTime}ms)`);
  } catch (error) {
    console.error(`❌ RPC 管理器失败:`, error.message);
  }
}

/**
 * 测试 RPC 客户端创建
 */
async function testRpcClient() {
  console.log('\n🔌 测试 RPC 客户端创建...');
  
  try {
    const startTime = Date.now();
    const client = await createRpcClient(97, {
      timeout: 5000,
      retryCount: 2,
      retryDelay: 500,
    });
    const endTime = Date.now();
    
    console.log(`✅ 客户端创建成功 (耗时: ${endTime - startTime}ms)`);
    
    // 测试基本功能
    const blockNumber = await client.getBlockNumber();
    console.log(`📦 当前区块号: ${blockNumber}`);
    
  } catch (error) {
    console.error(`❌ 客户端创建失败:`, error.message);
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 开始 RPC 超时修复测试\n');
  
  await testRpcManager();
  await testRpcClient();
  await testRpcTimeout();
  
  console.log('\n✨ 测试完成');
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export {
  testRpcTimeout,
  testRpcManager,
  testRpcClient,
  runTests
};

import React, { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { toast } from 'react-hot-toast'
import { formatUnits } from 'ethers'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { createPublicClient, createWalletClient, custom, http } from 'viem'
import { bscTestnet } from 'viem/chains'
import { parseContractError, formatTransactionError, isUserRejectedError } from '@/utils/contractErrorHandler'
import './NodeStakingSimplified.css'

export default function NodeStakingSimplified() {
  const { address: account, isConnected } = useAccount()

  // 简化的状态管理 - 直接在组件内部管理所有状态
  const [stakingData, setStakingData] = useState({
    isNodeActive: false,
    currentRequiredStake: '0',
    totalEffectiveNodes: 0,
    maxNodes: 1000,
    totalDividends: '0',
    dailyRewardPerNode: '0',
    canClaimToday: false,
    hasClaimedToday: false,
    lastCalculateTime: 0,
    canCalculateToday: false,
    activationTime: 0
  })

  // 按钮状态 - 简单直接的状态管理
  const [buttonStates, setButtonStates] = useState({
    canCalculate: false,
    canClaim: false,
    claimReason: '',
    calculateReason: ''
  })

  const [isLoading, setIsLoading] = useState(false)
  const [isStaking, setIsStaking] = useState(false)
  const [isUnstaking, setIsUnstaking] = useState(false)
  const [isClaiming, setIsClaiming] = useState(false)
  const [isCalculating, setIsCalculating] = useState(false)
  const [autoRefreshInterval, setAutoRefreshInterval] = useState(null)

  // 创建客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  })

  // 加载简化的质押数据
  const loadStakingData = async () => {
    if (!account || !isConnected) {
      // 未连接钱包时，重置为默认状态而不是直接返回
      setStakingData({
        isNodeActive: false,
        currentRequiredStake: '0',
        totalEffectiveNodes: 0,
        maxNodes: 1000,
        totalDividends: '0',
        dailyRewardPerNode: '0',
        canClaimToday: false,
        hasClaimedToday: false,
        lastCalculateTime: 0,
        canCalculateToday: false,
        activationTime: 0
      })
      setButtonStates({
        canCalculate: false,
        canClaim: false,
        claimReason: '请先连接钱包',
        calculateReason: '请先连接钱包'
      })
      return
    }

    setIsLoading(true)
    try {
      const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking

      // 并行查询所有数据
      const [
        isActive,
        requiredStake,
        userStakedAmount,
        totalNodes,
        maxNodes,
        dividendDetails,
        userStatus,
        activationTime,
        contractUSDTBalance
      ] = await Promise.all([
        publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'isNodeActive',
          args: [account]
        }).catch(() => false),
        publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'getCurrentRequiredStake'
        }).catch(() => 0n),
        // 从QPTLocker合约获取用户质押数量
        publicClient.readContract({
          address: CONTRACT_ADDRESSES[97].QPTLocker,
          abi: ABIS.QPTLocker,
          functionName: 'getUserLockedAmount',
          args: [account]
        }).catch(error => {
          console.error('❌ 获取用户质押数量失败:', error);
          return 0n;
        }),
        publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'totalEffectiveNodes'
        }).catch(() => 0n),
        publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'MAX_NODES'
        }).catch(() => 1000n),
        publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'getDividendDetails'
        }).catch(() => ({
          totalDividends: 0n,
          availableDividendPool: 0n,
          totalEffectiveNodes: 0n,
          rewardPerNode: 0n,
          lastCalculateTime: 0n,
          canCalculateToday: false
        })),
        publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'getUserNodeStatus',
          args: [account]
        }).catch(() => ({
          user: account,
          isActive: false,
          dailyReward: 0n,
          canClaimToday: false,
          hasClaimedToday: false,
          lastClaimDate: 0n,
          activationTime: 0n
        })),
        publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'getNodeActivationTime',
          args: [account]
        }).catch(() => 0n),
        // 直接获取合约USDT余额
        publicClient.readContract({
          address: CONTRACT_ADDRESSES[97].USDT,
          abi: ABIS.QPTToken, // USDT使用相同的ERC20 ABI
          functionName: 'balanceOf',
          args: [stakingAddress]
        }).catch(() => 0n)
      ])

      // 更新状态
      const newStakingData = {
        isNodeActive: isActive,
        currentRequiredStake: formatUnits(requiredStake, 18),
        userStakedAmount: formatUnits(userStakedAmount, 18),
        totalEffectiveNodes: Number(totalNodes),
        maxNodes: Number(maxNodes),
        totalDividends: formatUnits(contractUSDTBalance || dividendDetails.totalDividends || 0n, 6),
        availableDividendPool: formatUnits(dividendDetails.availableDividendPool || 0n, 6),
        dailyRewardPerNode: formatUnits(dividendDetails.rewardPerNode || 0n, 6),
        canClaimToday: userStatus.canClaimToday || false,
        hasClaimedToday: userStatus.hasClaimedToday || false,
        lastCalculateTime: Number(dividendDetails.lastCalculateTime || 0n),
        canCalculateToday: dividendDetails.canCalculateToday || false,
        activationTime: Number(activationTime)
      };

      setStakingData(newStakingData);

      // 简单直接的按钮状态判断
      updateButtonStates(newStakingData);

    } catch (error) {
      console.error('加载质押数据失败:', error)
      // 出错时设置默认状态，确保页面仍然可以显示
      setStakingData({
        isNodeActive: false,
        currentRequiredStake: '1030',
        totalEffectiveNodes: 0,
        maxNodes: 1000,
        totalDividends: '0',
        dailyRewardPerNode: '0',
        canClaimToday: false,
        hasClaimedToday: false,
        lastCalculateTime: 0,
        canCalculateToday: false,
        activationTime: 0
      })
      setButtonStates({
        canCalculate: false,
        canClaim: false,
        claimReason: '数据加载失败，请刷新重试',
        calculateReason: '数据加载失败，请刷新重试'
      })
      // 只在严重错误时显示toast，避免频繁弹窗
      if (error.message && !error.message.includes('User rejected')) {
        toast.error('加载数据失败，请刷新重试')
      }
    } finally {
      setIsLoading(false)
    }
  }

  // 简单直接的按钮状态更新函数
  const updateButtonStates = (data) => {
    const newButtonStates = {
      canCalculate: false,
      canClaim: false,
      claimReason: '',
      calculateReason: ''
    };

    // 只有节点激活的用户才能看到操作按钮
    if (!data.isNodeActive) {
      newButtonStates.claimReason = '节点未激活';
      newButtonStates.calculateReason = '节点未激活';

      setButtonStates(newButtonStates);
      return;
    }

    // 使用合约返回的状态（合约已修复）

    // 简化的分红计算逻辑：
    // 1. 如果今日可以计算分红且有余额，显示"计算今日分红"按钮
    // 2. 如果今日已计算分红，显示"领取分红"按钮（如果未领取）

    if (data.canCalculateToday) {
      // 今日分红未计算，检查是否有分红金额
      if (data.totalDividends > 0) {
        // 有分红金额才显示计算按钮
        newButtonStates.canCalculate = true;
        newButtonStates.claimReason = '请先计算今日分红';
      } else {
        // 没有分红金额，不显示计算按钮
        newButtonStates.calculateReason = '暂无分红金额可计算';
        newButtonStates.claimReason = '暂无分红可领取';
      }
    } else {
      // 今日分红已计算，检查领取状态
      newButtonStates.calculateReason = '今日已计算分红';

      if (data.hasClaimedToday) {
        newButtonStates.claimReason = '今日已领取';
      } else if (data.canClaimToday) {
        newButtonStates.canClaim = true;
      } else {
        newButtonStates.claimReason = '暂不可领取';
      }
    }

    setButtonStates(newButtonStates);
  };

  // 质押成为节点
  const handleStakeNode = async () => {
    if (!account) return

    // 检查用户权限
    try {
      const { checkUserPermission, PERMISSION_TYPES } = await import('@/utils/permissionManager');
      const permission = await checkUserPermission(account, PERMISSION_TYPES.ALL_OPERATIONS);
      if (!permission.allowed) {
        toast.error(permission.reason, {
          duration: 8000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      toast.error('权限检查失败，请重试');
      return;
    }

    setIsStaking(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking
      const qptTokenAddress = CONTRACT_ADDRESSES[97].QPTToken
      const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker

      // 获取当前质押要求
      const requiredStake = await publicClient.readContract({
        address: stakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'getCurrentRequiredStake'
      })

      // 检查QPT余额
      const qptBalance = await publicClient.readContract({
        address: qptTokenAddress,
        abi: ABIS.QPTToken,
        functionName: 'balanceOf',
        args: [account]
      })

      if (qptBalance < requiredStake) {
        toast.error(`QPT余额不足！需要 ${formatUnits(requiredStake, 18)} QPT，当前余额 ${formatUnits(qptBalance, 18)} QPT`)
        return
      }

      // 检查授权额度
      const currentAllowance = await publicClient.readContract({
        address: qptTokenAddress,
        abi: ABIS.QPTToken,
        functionName: 'allowance',
        args: [account, qptLockerAddress]
      })

      // 如果授权不足，先进行授权
      if (currentAllowance < requiredStake) {
        toast.success('第1步：正在授权QPT代币...')

        const approveHash = await walletClient.writeContract({
          address: qptTokenAddress,
          abi: ABIS.QPTToken,
          functionName: 'approve',
          args: [qptLockerAddress, requiredStake],
          account
        })

        toast.success('授权交易已提交，等待确认...')
        await publicClient.waitForTransactionReceipt({ hash: approveHash })
        toast.success('QPT授权成功！')
      }

      // 执行质押
      toast.success('第2步：正在执行质押操作...')

      const stakeHash = await walletClient.writeContract({
        address: stakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'stakeNode',
        account
      })

      toast.success('质押交易已提交，等待确认...')
      await publicClient.waitForTransactionReceipt({ hash: stakeHash })
      toast.success('🎉 成功激活节点！')

      // 刷新数据
      await loadStakingData()
      if (window.refreshTokenBalances) {
        window.refreshTokenBalances()
      }

    } catch (error) {
      console.error('质押失败:', error)
      toast.error('质押失败: ' + (error.message || '未知错误'))
    } finally {
      setIsStaking(false)
    }
  }

  // 取消质押
  const handleUnstakeNode = async () => {
    if (!account) return

    setIsUnstaking(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking

      const hash = await walletClient.writeContract({
        address: stakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'unstakeNode',
        account
      })

      toast.success('取消质押交易已提交，等待确认...')
      await publicClient.waitForTransactionReceipt({ hash })
      toast.success('成功取消质押！')

      // 刷新数据
      await loadStakingData()
      if (window.refreshTokenBalances) {
        window.refreshTokenBalances()
      }

    } catch (error) {
      console.error('取消质押失败:', error)
      toast.error('取消质押失败: ' + (error.message || '未知错误'))
    } finally {
      setIsUnstaking(false)
    }
  }

  // 领取分红
  const handleClaimReward = async () => {
    if (!account) {
      return;
    }

    // 简单直接的按钮状态检查
    if (!buttonStates.canClaim) {
      toast.error(buttonStates.claimReason);
      return;
    }

    // 检查用户是否已注册代理系统
    try {
      const { checkUserRegistered } = await import('@/apis/agentSystemApi');
      const isRegistered = await checkUserRegistered({ userAddress: account });

      if (!isRegistered) {
        toast.error('请先注册代理系统才能领取节点奖励！', {
          duration: 5000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('❌ 检查注册状态失败:', error);
      toast.error('检查注册状态失败，请重试');
      return;
    }

    setIsClaiming(true)
    let transactionHash = null

    try {
      const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking

      // 交易前验证 - 使用当前状态数据进行验证
      if (!stakingData.isNodeActive) {
        toast.error('您不是活跃节点，无法领取分红');
        return;
      }
      if (stakingData.hasClaimedToday) {
        toast.error('今日已经领取过分红了');
        return;
      }
      if (!stakingData.canClaimToday) {
        toast.error('今日分红尚未计算，请先计算今日分红');
        return;
      }

      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      // 在提交交易前，先验证QPT质押状态
      const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker;

      try {
        const userLockedAmount = await publicClient.readContract({
          address: qptLockerAddress,
          abi: ABIS.QPTLocker,
          functionName: 'getUserLockedAmount',
          args: [account]
        });

        if (userLockedAmount <= 0) {
          toast.error('QPT质押数据异常，请检查质押状态');
          return;
        }
      } catch (qptError) {
        console.error('❌ QPT质押验证失败:', qptError);
        toast.error('无法验证QPT质押状态，请重试');
        return;
      }

      // 提交交易
      transactionHash = await walletClient.writeContract({
        address: stakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'claimReward',
        account
      })

      toast.success('领取分红交易已提交，等待确认...', { id: 'claim-pending' })

      // 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({
        hash: transactionHash,
        timeout: 60000 // 60秒超时
      })

      // 检查交易状态
      if (receipt.status === 'success') {
        toast.success('🎉 成功领取分红！', { id: 'claim-pending' })

        // 立即更新按钮状态为已领取
        setButtonStates(prev => ({
          ...prev,
          canClaim: false,
          claimReason: '今日已领取'
        }));

        // 刷新数据
        await loadStakingData()
        if (window.refreshTokenBalances) {
          window.refreshTokenBalances()
        }
      } else {
        console.error('❌ 交易执行失败, receipt status:', receipt.status);

        // 尝试获取更详细的revert原因
        try {
          // 重新模拟调用来获取具体的revert原因
          await publicClient.simulateContract({
            address: stakingAddress,
            abi: ABIS.NodeStaking,
            functionName: 'claimReward',
            account
          });
        } catch (simulateError) {


          // 尝试解析具体的revert消息
          let revertReason = 'Unknown revert reason';
          if (simulateError.message) {
            if (simulateError.message.includes('Already claimed today')) {
              revertReason = '今日已经领取过分红了';
            } else if (simulateError.message.includes('Not an active node')) {
              revertReason = '您不是活跃节点，无法领取分红';
            } else if (simulateError.message.includes('Daily reward not calculated yet')) {
              revertReason = '今日分红尚未计算，请先计算今日分红';
            } else if (simulateError.message.includes('No reward available')) {
              revertReason = '暂无可领取的分红';
            } else if (simulateError.message.includes('QPT stake verification failed')) {
              revertReason = 'QPT质押验证失败，请检查质押状态';
            } else if (simulateError.message.includes('Insufficient contract balance')) {
              revertReason = '合约余额不足，请联系管理员';
            } else if (simulateError.message.includes('execution reverted')) {
              // 尝试从消息中提取revert原因
              const match = simulateError.message.match(/execution reverted: (.+)/);
              if (match) {
                revertReason = match[1];
              }
            } else {
              revertReason = simulateError.shortMessage || simulateError.message;
            }
          }

          throw new Error(`交易被revert: ${revertReason}`);
        }

        throw new Error(`交易被revert，状态: ${receipt.status}`)
      }

    } catch (error) {
      console.error('❌ 领取分红失败:', {
        error: error,
        message: error.message,
        shortMessage: error.shortMessage,
        code: error.code,
        data: error.data,
        transactionHash: transactionHash,
        stack: error.stack
      });

      // 使用新的错误处理工具
      const errorMessage = isUserRejectedError(error)
        ? '用户取消了交易'
        : formatTransactionError(error, transactionHash, '领取分红');

      toast.error(errorMessage, { id: 'claim-pending' })

      // 如果有交易哈希但失败了，不刷新数据避免显示错误状态
      if (!transactionHash) {

        // 只有在没有提交交易的情况下才刷新数据
        await loadStakingData()
        forceRefreshButtonStatus();
      }
    } finally {
      setIsClaiming(false)
    }
  }

  // 启动自动刷新（当有人可能正在计算分红时）
  const startAutoRefresh = () => {
    if (autoRefreshInterval) return // 避免重复启动

    const interval = setInterval(async () => {
      try {
        await loadStakingData()
      } catch (error) {
        console.error('自动刷新失败:', error)
      }
    }, 3000) // 每3秒刷新一次

    setAutoRefreshInterval(interval)

    // 30秒后停止自动刷新
    setTimeout(() => {
      clearInterval(interval)
      setAutoRefreshInterval(null)
    }, 30000)
  }

  // 停止自动刷新
  const stopAutoRefresh = () => {
    if (autoRefreshInterval) {
      clearInterval(autoRefreshInterval)
      setAutoRefreshInterval(null)
    }
  }

  // 计算今日分红（管理员功能）
  const handleCalculateDailyReward = async () => {
    if (!account) {
      return;
    }

    // 简单直接的按钮状态检查
    if (!buttonStates.canCalculate) {
      toast.error(buttonStates.calculateReason);
      return;
    }

    setIsCalculating(true)
    // 启动自动刷新，让其他用户能看到状态变化
    startAutoRefresh()
    let transactionHash = null;

    try {
      const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking

      // 交易前验证 - 使用合约状态（合约已修复）
      if (!stakingData.canCalculateToday) {
        toast.error('今日已计算过分红或不满足计算条件');
        return;
      }
      if (stakingData.totalEffectiveNodes === 0) {
        toast.error('当前没有有效节点，无法计算分红');
        return;
      }
      if (stakingData.totalDividends === 0) {
        toast.error('当前没有分红金额，无法计算分红');
        return;
      }

      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      transactionHash = await walletClient.writeContract({
        address: stakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'calculateDailyReward',
        account
      })

      toast.success('计算分红交易已提交，等待确认...')

      const receipt = await publicClient.waitForTransactionReceipt({ hash: transactionHash })

      if (receipt.status === 'success') {
        toast.success('🎉 今日分红计算完成！')

        // 立即更新按钮状态为已计算
        setButtonStates(prev => ({
          ...prev,
          canCalculate: false,
          calculateReason: '今日已计算或不满足条件'
        }));

        // 刷新数据
        await loadStakingData()

        // 通知其他组件刷新（如果存在全局刷新函数）
        if (window.refreshNodeStakingData) {
          window.refreshNodeStakingData()
        }
      } else {
        console.error('❌ 计算分红交易执行失败, receipt status:', receipt.status);

        // 尝试获取更详细的revert原因
        try {
          // 重新模拟调用来获取具体的revert原因
          await publicClient.simulateContract({
            address: stakingAddress,
            abi: ABIS.NodeStaking,
            functionName: 'calculateDailyReward',
            account
          });
        } catch (simulateError) {


          // 尝试解析具体的revert消息
          let revertReason = 'Unknown revert reason';
          if (simulateError.message) {
            if (simulateError.message.includes('Already calculated for today')) {
              revertReason = '今日已经计算过分红了';
            } else if (simulateError.message.includes('execution reverted')) {
              // 尝试从消息中提取revert原因
              const match = simulateError.message.match(/execution reverted: (.+)/);
              if (match) {
                revertReason = match[1];
              }
            } else {
              revertReason = simulateError.shortMessage || simulateError.message;
            }
          }

          throw new Error(`交易被revert: ${revertReason}`);
        }

        throw new Error(`交易被revert，状态: ${receipt.status}`)
      }

    } catch (error) {
      console.error('❌ 计算分红失败:', {
        error: error,
        message: error.message,
        shortMessage: error.shortMessage,
        code: error.code,
        data: error.data,
        transactionHash: transactionHash,
        stack: error.stack
      });

      // 使用新的错误处理工具
      const errorMessage = isUserRejectedError(error)
        ? '用户取消了交易'
        : formatTransactionError(error, transactionHash, '计算分红');

      toast.error(errorMessage)
    } finally {
      setIsCalculating(false)
      stopAutoRefresh()
    }
  }

  // 格式化时间
  const formatTime = (daysSinceEpoch) => {
    if (!daysSinceEpoch || daysSinceEpoch === 0) return '未计算'
    // currentDay是以天数为单位的，需要转换为实际时间戳
    // daysSinceEpoch * 24 * 60 * 60 * 1000 = 实际时间戳
    const timestamp = daysSinceEpoch * 24 * 60 * 60 * 1000
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 组件挂载时加载数据
  useEffect(() => {
    if (account && isConnected) {
      loadStakingData()
    }
  }, [account, isConnected])

  // 定期检查分红状态变化（每30秒检查一次）
  useEffect(() => {
    if (!account || !isConnected) return

    // 使用 ref 来存储最新的状态值，避免依赖问题
    let lastCanCalculate = stakingData.canCalculateToday
    let lastCalculateTime = stakingData.lastCalculateTime

    const statusCheckInterval = setInterval(async () => {
      try {
        const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking
        const dividendDetails = await publicClient.readContract({
          address: stakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'getDividendDetails'
        }).catch(() => ({
          canCalculateToday: false,
          lastCalculateTime: 0n
        }))

        // 如果状态发生变化，刷新数据
        const newCanCalculate = dividendDetails.canCalculateToday || false
        const newLastCalculateTime = Number(dividendDetails.lastCalculateTime || 0n)

        if (newCanCalculate !== lastCanCalculate ||
            newLastCalculateTime !== lastCalculateTime) {
          lastCanCalculate = newCanCalculate
          lastCalculateTime = newLastCalculateTime
          await loadStakingData()
        }
      } catch (error) {
        console.error('状态检查失败:', error)
      }
    }, 30000) // 每30秒检查一次

    return () => clearInterval(statusCheckInterval)
  }, [account, isConnected]) // 移除 stakingData 依赖，避免无限循环

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval)
      }
    }
  }, [autoRefreshInterval])

  return (
    <div className="node-staking-simplified">
      <div className="module-header">
        <div className="header-content">
          <h3>🏗️ 节点质押</h3>
        </div>
        <button
          className="refresh-btn"
          onClick={loadStakingData}
          disabled={isLoading}
        >
          {isLoading ? '🔄' : '🔄 刷新'}
        </button>
      </div>

      {/* 加载状态提示 */}
      {isLoading && (
        <div className="loading-indicator" style={{
          textAlign: 'center',
          padding: '20px',
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: '14px'
        }}>
          <div style={{ marginBottom: '10px' }}>🔄 正在加载数据...</div>
        </div>
      )}

      {/* 节点状态卡片 */}
      <div className="node-status-card">
        <div className="status-indicator">
          {stakingData.isNodeActive ? '🟢' : '🔴'}
        </div>
        <div className="status-info">
          <div className="status-title">
            {stakingData.isNodeActive ? '节点已激活' : '未激活节点'}
          </div>
          <div className="status-desc">
            {stakingData.isNodeActive
              ? '您是活跃节点，可以领取分红'
              : '质押QPT即可成为节点'}
          </div>
        </div>
        {/* 质押数量显示 */}
        {stakingData.isNodeActive && (
          <div className="staking-amount">
            <div className="staking-label">我的质押</div>
            <div className="staking-value">
              {stakingData.userStakedAmount || '0'} QPT
            </div>
          </div>
        )}
      </div>

      {/* 简化的分红信息 */}
      <div className="dividend-info-simplified">
        <h4>💰 分红信息</h4>
        <div className="dividend-grid">
          <div className="dividend-item" style={{
            background: 'rgba(0, 0, 0, 0.4)',
            border: '1px solid rgba(255, 255, 255, 0.4)',
            backdropFilter: 'blur(5px)'
          }}>
            <span className="label" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>分红池余额</span>
            <span className="value" style={{ color: '#10b981' }}>{stakingData.totalDividends} USDT</span>
          </div>

          <div className="dividend-item highlight" style={{
            background: 'rgba(255, 215, 0, 0.3)',
            border: '1px solid rgba(255, 215, 0, 0.6)',
            backdropFilter: 'blur(5px)'
          }}>
            <span className="label" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>每节点可领取</span>
            <span className="value" style={{ color: '#ffd700' }}>{stakingData.dailyRewardPerNode} USDT</span>
          </div>
          <div className="dividend-item" style={{
            background: 'rgba(0, 0, 0, 0.4)',
            border: '1px solid rgba(255, 255, 255, 0.4)',
            backdropFilter: 'blur(5px)'
          }}>
            <span className="label" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>有效节点数</span>
            <span className="value" style={{ color: 'white' }}>{stakingData.totalEffectiveNodes}/{stakingData.maxNodes}</span>
          </div>
          <div className="dividend-item" style={{
            background: 'rgba(0, 0, 0, 0.4)',
            border: '1px solid rgba(255, 255, 255, 0.4)',
            backdropFilter: 'blur(5px)'
          }}>
            <span className="label" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>剩余节点</span>
            <span className="value" style={{ color: 'white' }}>{stakingData.maxNodes - stakingData.totalEffectiveNodes}</span>
          </div>
        </div>
      </div>

      {/* 分红计算状态 - 只对激活节点用户显示 */}
      {stakingData.isNodeActive && (
        <div className="calculate-status">
          <h4>📊 分红计算状态</h4>
          <div className="calculate-info">
            <div className="info-item">
              <span className="label">上次计算时间:</span>
              <span className="value">{formatTime(stakingData.lastCalculateTime)}</span>
            </div>
            <div className="info-item">
              <span className="label">今日可计算:</span>
              <span className={`value ${stakingData.canCalculateToday ? 'can-calculate' : 'cannot-calculate'}`}>
                {stakingData.canCalculateToday ? '是' : '否 (24小时内已计算)'}
              </span>
            </div>
          </div>
          <button
            className={`calculate-btn ${!buttonStates.canCalculate ? 'disabled' : ''}`}
            onClick={handleCalculateDailyReward}
            disabled={isCalculating || !buttonStates.canCalculate}
            title={!buttonStates.canCalculate ? buttonStates.calculateReason : ''}
          >
            {isCalculating ? '计算中...' : '计算今日分红'}
          </button>
          <div className="calculate-hint">
            <span className="hint-icon">💡</span>
            <span className="hint-text">
              作为激活节点，您可以计算今日分红。计算后所有节点都可以领取分红。
            </span>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="action-buttons">
        {!stakingData.isNodeActive ? (
          <button
            className="stake-btn"
            onClick={handleStakeNode}
            disabled={isStaking}
          >
            {isStaking ? '激活中...' : `激活节点 (${stakingData.currentRequiredStake} QPT)`}
          </button>
        ) : (
          <div className="active-node-actions">
            <button
              className={`claim-btn ${!buttonStates.canClaim ? 'disabled' : ''}`}
              onClick={handleClaimReward}
              disabled={isClaiming || !buttonStates.canClaim}
              title={!buttonStates.canClaim ? buttonStates.claimReason : ''}
            >
              {isClaiming ? '领取中...' :
               stakingData.hasClaimedToday ? '今日已领取' :
               buttonStates.canClaim ? `领取分红 (${stakingData.dailyRewardPerNode} USDT)` :
               buttonStates.claimReason}
            </button>
            <button
              className="unstake-btn"
              onClick={handleUnstakeNode}
              disabled={isUnstaking}
            >
              {isUnstaking ? '取消中...' : '取消质押'}
            </button>
          </div>
        )}
      </div>

      {/* 分红规则说明 */}
      <div className="simplified-rules">
        <h4>📋 分红规则</h4>
        <div className="rules-content">
          <div className="rule-item">
            <span className="rule-title">💡 分红计算:</span>
            <span className="rule-desc">总余额 ÷ 有效节点数 = 每节点分红</span>
          </div>
          <div className="rule-item">
            <span className="rule-title">⏰ 分红周期:</span>
            <span className="rule-desc">每日08:00后可计算分红，24小时后自动失效</span>
          </div>
          <div className="rule-item">
            <span className="rule-title">🎯 领取条件:</span>
            <span className="rule-desc">节点激活 + 已计算分红 + 当日未领取</span>
          </div>
          <div className="rule-item">
            <span className="rule-title">🔒 质押要求:</span>
            <span className="rule-desc">每延迟一天重新质押，需要额外支付10 QPT</span>
          </div>
        </div>
      </div>
    </div>
  )
}

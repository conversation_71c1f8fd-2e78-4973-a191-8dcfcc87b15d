/* QPT回购组件样式 - 与拼团房间保持一致的宽度 */
.qpt-buyback {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
}

/* QPT回购房间档位颜色样式 - 与拼团房间保持一致 */
.qpt-buyback .room-card.tier-100 {
  border-left: 4px solid #28a745;
}

.qpt-buyback .room-card.tier-200 {
  border-left: 4px solid #007bff;
}

.qpt-buyback .room-card.tier-500 {
  border-left: 4px solid #8b5cf6;
}

.qpt-buyback .room-card.tier-1000 {
  border-left: 4px solid #f59e0b;
}

/* 档位对应的状态徽章颜色 */
.qpt-buyback .status-badge.tier-100 {
  background: #d4edda;
  color: #155724;
}

.qpt-buyback .status-badge.tier-200 {
  background: #cce7ff;
  color: #004085;
}

.qpt-buyback .status-badge.tier-500 {
  background: #e9d5ff;
  color: #6b21a8;
}

.qpt-buyback .status-badge.tier-1000 {
  background: #fef3c7;
  color: #92400e;
}

/* 模块头部 */
.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.module-header h3 {
  font-size: 20px;
  font-weight: bold;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-header p {
  font-size: 14px;
  color: #64748b;
  margin: 4px 0 0 0;
}

.refresh-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #475569;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #e2e8f0;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 连接提示 */
.connect-prompt {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

/* 回购池信息 */
.buyback-pool {
  margin-bottom: 24px;
}

.pool-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.pool-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  border-radius: 12px;
  color: white;
  text-align: center;
}

.pool-item:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.pool-item:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.pool-item:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.pool-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.pool-value {
  font-size: 24px;
  font-weight: bold;
}

/* 新的单一回购池显示样式 */
.pool-info-single {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pool-item-main {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 50%, #ffdd59 100%);
  color: white;
  padding: 40px 60px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
  position: relative;
  overflow: hidden;
  min-width: 400px;
  transform: perspective(1000px) rotateX(5deg);
  transition: all 0.3s ease;
}

.pool-item-main:hover {
  transform: perspective(1000px) rotateX(0deg) scale(1.02);
  box-shadow: 0 12px 40px rgba(255, 107, 107, 0.5);
}

.pool-item-main::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.pool-label-main {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.pool-value-main {
  font-size: 42px;
  font-weight: 900;
  margin: 8px 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
  line-height: 1.1;
}

/* 回购池信息样式 - 参考财务中心 */
.staking-info {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

/* 强制确保所有信息项都是横向布局 */
.staking-info .info-item {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.staking-info .info-label,
.staking-info .info-value {
  display: inline !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 限制在QPT回购页面作用域内 */
.qpt-buyback .info-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 0;
  flex-direction: row !important;
}

.qpt-buyback .info-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  flex-shrink: 0 !important;
  display: inline !important;
  margin-bottom: 0 !important;
}

.qpt-buyback .info-value {
  font-size: 16px;
  font-weight: bold;
  color: #1f2937;
  text-align: right !important;
  flex-shrink: 0 !important;
  display: inline !important;
}

.pool-item-fullwidth::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 4s infinite;
}

.pool-item-fullwidth .pool-label-main {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.pool-item-fullwidth .pool-value-main {
  font-size: 48px;
  font-weight: 900;
  margin: 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
  line-height: 1.1;
}

/* 移除余额信息样式，使用顶部导航栏的余额显示 */

/* 移除创建房间样式，功能已移至管理后台 */

/* 房间列表 */
.active-rooms,
.user-rooms {
  margin-bottom: 32px;
}

.active-rooms h4,
.user-rooms h4 {
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rooms-list {
  display: grid;
  gap: 16px;
}

/* QPT回购页面专用的房间网格样式 - 使用更具体的选择器避免冲突 */
.qpt-buyback .rooms-grid {
  display: grid;
  grid-template-columns: 1fr !important;  /* 强制竖排布局，与拼团房间一致 */
  gap: 1rem;
}

.room-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  text-align: left;
}

/* 强制覆盖所有子元素的居中样式 - 确保QPT回购房间信息居左对齐 */
.qpt-buyback .room-card,
.qpt-buyback .room-card *,
.qpt-buyback .room-card .room-info,
.qpt-buyback .room-card .info-row,
.qpt-buyback .room-card .info-row *,
.qpt-buyback .room-card .room-header,
.qpt-buyback .room-card .room-header *,
.qpt-buyback .room-card .label,
.qpt-buyback .room-card .value {
  text-align: left !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
}

/* 确保房间信息行的布局 - 参考拼团房间样式 */
.qpt-buyback .room-card .info-row {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  text-align: left !important;
  margin-bottom: 12px !important;
  padding: 8px 0 !important;
  width: 100% !important;
}

.qpt-buyback .room-card .info-row .label {
  font-weight: 500;
  color: #64748b;
  text-align: left !important;
  margin-right: 8px !important;
  min-width: 80px !important;
  flex-shrink: 0 !important;
}

.qpt-buyback .room-card .info-row .value {
  font-weight: 600;
  color: #1e293b;
  text-align: left !important;
  flex: 1 !important;
}

/* 房间信息区域整体布局 */
.qpt-buyback .room-card .room-info {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  width: 100% !important;
  margin-bottom: 16px !important;
}

/* QPT回购房间参与状态样式 - 参考拼团房间 */
.qpt-buyback .room-status-info.participated {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  border: 2px solid #16a34a;
  color: #15803d;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px; /* 调整间距，避免与下方内容看起来像两部分 */
}

.qpt-buyback .room-status-info.waiting {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 2px solid #f59e0b;
  color: #92400e;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px; /* 调整间距，保持一致性 */
}

/* QPT验证状态样式 - 全宽度响应式 */
.qpt-verification-container {
  width: 100%;
  margin: 8px 0;
}

.qpt-verification-content {
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qpt-verification-content.loading {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(156, 163, 175, 0.3);
  color: #6b7280;
}

.qpt-verification-content.verified {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #15803d;
}

.qpt-verification-content.failed {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
}

.verification-main {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.verification-requirement {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.8;
}

.room-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 房间状态样式 */
.room-card.status-active {
  border-left: 4px solid #10b981;
}

.room-card.status-warning {
  border-left: 4px solid #f59e0b;
}

.room-card.status-success {
  border-left: 4px solid #3b82f6;
}

.room-card.status-expired {
  border-left: 4px solid #ef4444;
  opacity: 0.7;
}

.user-room {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 12px;
}

.room-title {
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.status-badges {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: flex-end;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.success {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.expired {
  background: #fee2e2;
  color: #991b1b;
}

.status-badge.created {
  background: #10b981;
  color: white;
}

.status-badge.participated {
  background: #3b82f6;
  color: white;
}

.status-badge.winner {
  background: #f59e0b;
  color: white;
}

.room-id {
  font-size: 16px;
  font-weight: bold;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.creator-badge {
  background: #10b981;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.winner-badge {
  background: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.room-status {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.room-status.active {
  background: #dcfce7;
  color: #166534;
}

.room-status.warning {
  background: #fef3c7;
  color: #92400e;
}

.room-status.success {
  background: #dbeafe;
  color: #1e40af;
}

.room-status.error {
  background: #fee2e2;
  color: #991b1b;
}

.room-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row.winner-info {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  padding: 12px;
  border-radius: 8px;
  border: none;
  margin-top: 8px;
}

.info-row .label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.info-row .value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 600;
}

.info-row .value.expired {
  color: #ef4444;
}

.room-actions {
  margin-top: 20px;
}

/* 详细信息显示样式 */
.detailed-info {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

/* 限制在QPT回购页面作用域内 */
.qpt-buyback .info-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
  flex-direction: row !important;
}

.qpt-buyback .info-item:last-child {
  border-bottom: none;
}

.qpt-buyback .info-item.highlight {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  padding: 12px;
  border-radius: 8px;
  margin-top: 8px;
  border: 2px solid #f59e0b;
}

.qpt-buyback .info-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  flex-shrink: 0 !important;
  display: inline !important;
  margin-bottom: 0 !important;
}

.qpt-buyback .info-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 600;
  text-align: right !important;
  flex-shrink: 0 !important;
  display: inline !important;
}

.qpt-buyback .info-item.highlight .info-label,
.qpt-buyback .info-item.highlight .info-value {
  color: #92400e;
  font-weight: 700;
}

/* 房间状态信息样式 - 参考拼团房间 */
.room-status-info {
  width: 100%;
  padding: 14px 20px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.room-status-info.participated {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  border: 2px solid #10b981;
}

/* 参与者排序显示样式 - 参考拼团房间 */
.participants-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.participant-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.participant-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.participant-item.winner {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

.participant-rank {
  font-weight: 600;
  color: #475569;
  min-width: 24px;
  text-align: center;
}

.participant-address {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  color: #64748b;
  flex: 1;
}

.participant-self {
  color: #3b82f6;
  font-weight: 600;
  font-size: 12px;
  margin-left: 4px; /* 增加与地址的间距，参考拼团房间 */
}

.participant-winner {
  color: #f59e0b;
  font-weight: 600;
  font-size: 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 开奖按钮样式 */
.creator-lottery-btn {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.creator-lottery-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.creator-lottery-btn:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 房间状态信息样式扩展 */
.room-status-info.completed {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border: 2px solid #f59e0b;
}

.room-status-info.waiting {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border: 2px solid #3b82f6;
}

/* 移动端响应式设计 - 参考拼团房间 */
@media (max-width: 768px) {
  .qpt-buyback {
    padding: 0 12px;
  }

  .qpt-buyback .rooms-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem;
    padding: 0;
  }

  .room-card {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 12px;
  }

  .room-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .room-title {
    font-size: 1.1rem;
  }

  .status-badge {
    font-size: 0.8rem;
    padding: 3px 10px;
  }

  .info-row {
    padding: 6px 0;
  }

  .info-row .label {
    font-size: 0.9rem;
  }

  .info-row .value {
    font-size: 0.9rem;
  }

  .join-btn {
    padding: 14px 16px;
    font-size: 1rem;
    width: 100%;
  }

  .creator-lottery-btn {
    padding: 14px 16px;
    font-size: 1rem;
    width: 100%;
  }

  .room-status-info {
    padding: 12px 16px;
    font-size: 1rem;
  }

  .participants-list {
    gap: 2px !important;
    max-height: 150px !important;
  }

  .participant-item {
    padding: 2px 6px;
    font-size: 11px;
    gap: 6px;
  }

  .participant-rank {
    min-width: 20px;
  }

  .participant-address {
    font-size: 11px;
  }

  .participant-self,
  .participant-winner {
    font-size: 10px;
  }

  .participant-self {
    margin-left: 3px; /* 移动端稍微减少间距 */
  }
}

@media (max-width: 480px) {
  .qpt-buyback {
    padding: 0 8px;
  }

  .room-card {
    padding: 0.8rem;
    border-radius: 8px;
  }

  .room-title {
    font-size: 1rem;
  }

  .info-row .label,
  .info-row .value {
    font-size: 0.85rem;
  }

  .join-btn,
  .creator-lottery-btn {
    padding: 12px 14px;
    font-size: 0.9rem;
  }

  .room-status-info {
    padding: 10px 12px;
    font-size: 0.9rem;
  }

  .participant-item {
    padding: 1px 4px;
    font-size: 10px;
    gap: 4px;
  }

  .participant-rank {
    min-width: 18px;
    font-size: 10px;
  }

  .participant-address {
    font-size: 10px;
  }

  .participant-self,
  .participant-winner {
    font-size: 9px;
  }

  .participant-self {
    margin-left: 2px; /* 小屏幕进一步减少间距 */
  }
}

.room-detail {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.room-countdown {
  font-size: 14px;
  color: #dc2626;
  font-weight: 600;
  margin-top: 8px;
  padding: 8px 12px;
  background: #fef2f2;
  border-radius: 6px;
  border-left: 3px solid #dc2626;
}

.join-btn {
  width: 100%;
  padding: 14px 20px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 移除重复定义 - 已在下方统一定义 */

.join-btn.disabled {
  background: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.join-btn:disabled {
  background: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 规则说明 */
.rules-section {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.rules-section h4 {
  font-size: 16px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rule-item {
  font-size: 14px;
  color: #475569;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .qpt-buyback {
    padding: 16px;
  }

  .module-header {
    /* 保持横向排列，但调整间距 */
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
  }

  /* 标题区域在移动端保持紧凑 */
  .module-header h3 {
    font-size: 18px;
    margin-bottom: 2px;
  }

  .module-header p {
    font-size: 12px;
    margin: 0;
  }

  /* 刷新按钮在移动端稍微小一点 */
  .refresh-btn {
    padding: 6px 12px;
    font-size: 12px;
    flex-shrink: 0;
  }

  .pool-info {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* 确保信息网格在移动端是单列布局 */
  .info-grid {
    grid-template-columns: 1fr !important;
    gap: 0 !important;
  }

  /* 确保信息项在移动端保持横向排列 */
  .qpt-buyback .info-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    text-align: left !important;
    padding: 8px 0;
  }

  .qpt-buyback .info-label {
    font-size: 13px;
    flex-shrink: 0;
  }

  .qpt-buyback .info-value {
    font-size: 14px;
    text-align: right;
    flex-shrink: 0;
  }

  .pool-item {
    padding: 16px;
  }

  .pool-value {
    font-size: 20px;
  }

  /* 新的单一回购池响应式样式 */
  .pool-item-main {
    padding: 30px 40px;
    min-width: 300px;
  }

  .pool-value-main {
    font-size: 32px;
  }

  .pool-label-main {
    font-size: 16px;
  }

  /* 满屏回购池响应式样式 */
  .buyback-pool-fullwidth {
    margin-left: -16px;
    margin-right: -16px;
  }

  .pool-item-fullwidth {
    padding: 24px 16px;
  }

  .pool-item-fullwidth .pool-label-main {
    font-size: 18px;
  }

  .pool-item-fullwidth .pool-value-main {
    font-size: 36px;
  }
}

@media (max-width: 480px) {
  .qpt-buyback {
    padding: 12px;
  }

  .room-card {
    padding: 16px;
  }

  .room-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .create-btn {
    padding: 12px;
    font-size: 14px;
  }

  .rules-section {
    padding: 16px;
  }

  /* 确保信息网格在小屏幕移动端也是单列布局 */
  .info-grid {
    grid-template-columns: 1fr !important;
    gap: 0 !important;
  }

  /* 确保信息项在小屏幕移动端也保持横向排列 */
  .qpt-buyback .info-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    text-align: left !important;
    padding: 6px 0;
  }

  .qpt-buyback .info-label {
    font-size: 12px;
    flex-shrink: 0;
  }

  .qpt-buyback .info-value {
    font-size: 13px;
    text-align: right;
    flex-shrink: 0;
  }
}

/* 档位相关样式 */
.tier-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  margin-left: 8px;
}

/* 档位标识颜色 - 与拼团标准颜色完全一致 */
.tier-badge.tier-100 {
  background: #3B82F6 !important; /* 蓝色 */
}

.tier-badge.tier-200 {
  background: #8B5CF6 !important; /* 紫色 */
}

.tier-badge.tier-500 {
  background: #EF4444 !important; /* 红色 */
}

.tier-badge.tier-1000 {
  background: #14B8A6 !important; /* 青色 */
}

/* 房间标题样式调整 */
.room-title {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

/* 加入按钮档位颜色 - 与拼团标准颜色完全一致 */
.join-btn.tier-100 {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%) !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  color: white;
}

.join-btn.tier-200 {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%) !important;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.2);
  color: white;
}

.join-btn.tier-500 {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
  color: white;
}

.join-btn.tier-1000 {
  background: linear-gradient(135deg, #14B8A6 0%, #0F766E 100%) !important;
  box-shadow: 0 2px 4px rgba(20, 184, 166, 0.2);
  color: white;
}

/* 档位按钮悬停效果 - 与拼团标准颜色完全一致 */
.join-btn.tier-100:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.join-btn.tier-200:hover:not(:disabled) {
  background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
}

.join-btn.tier-500:hover:not(:disabled) {
  background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.join-btn.tier-1000:hover:not(:disabled) {
  background: linear-gradient(135deg, #0F766E 0%, #0D9488 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(20, 184, 166, 0.3);
}

/* 赢家奖励信息样式 - 参考拼团房间 */
.winner-reward-section {
  margin-top: 16px;
}

.winner-reward-container {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.winner-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.winner-icon {
  font-size: 20px;
}

.winner-text {
  font-size: 16px;
  font-weight: 700;
  color: #92400e;
}

.reward-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reward-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: left;
}

.reward-label {
  font-size: 14px;
  color: #78350f;
  font-weight: 500;
  flex-shrink: 0;
}

.reward-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  text-align: right;
}

.reward-value.winner-address {
  color: #1d4ed8;
  font-family: monospace;
}

.reward-value.amount {
  color: #059669;
}

.reward-value.description {
  color: #64748b;
  font-style: italic;
}

/* 领取奖励按钮样式 - 参考拼团房间 */
.claim-reward-btn {
  width: 100%;
  padding: 12px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 12px;
}

.claim-reward-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.claim-reward-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 房间状态信息样式 - 参考拼团房间 */
.room-status-info {
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
  text-align: center;
  font-weight: 500;
}

.room-status-info.completed {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border: 1px solid #10b981;
}

.room-status-info.expired {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 1px solid #ef4444;
}

.room-status-info.waiting {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border: 1px solid #f59e0b;
}

.room-status-info.participated {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  border: 2px solid #16a34a;
  color: #15803d;
}

/* 完成状态的头部 */
.completion-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
}

.winner-badge {
  color: #dc2626;
  font-weight: bold;
  margin-left: 8px;
}

/* 过期状态的头部 */
.expiry-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #991b1b;
}

/* 领取操作区域 */
.claim-actions {
  margin-top: 12px;
}

.reward-section {
  background: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e5e7eb;
}

.reward-info {
  margin-bottom: 12px;
}

.user-reward-info {
  text-align: left;
}

.reward-description {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.reward-details {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  line-height: 1.4;
}

/* 领取按钮样式 - 参考拼团房间 */
.claim-btn {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.claim-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.claim-btn.claimed {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  cursor: not-allowed;
}

.claim-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 退款相关样式 */
.refund-section {
  background: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  margin-top: 12px;
}

.refund-info {
  margin-bottom: 12px;
  text-align: left;
}

.refund-description {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.refund-details {
  font-size: 12px;
  color: #6b7280;
}

.refund-btn {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.refund-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.refund-btn.claimed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  cursor: not-allowed;
  color: white;
}

.refund-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 已退款状态样式 - 参考GroupBuyRoom */
.room-status-info.expired .expiry-header {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  text-align: center;
}

.refund-description {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  text-align: center;
}

/* 移除了规则说明中的颜色样式，改为纯文字显示 */

/* 房间头部样式 */
.rooms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.rooms-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
}

.pagination-info {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
  padding: 16px 0;
}

.pagination-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #475569;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  gap: 4px;
}

.pagination-number {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #475569;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.pagination-number:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.pagination-number.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.pagination-number.active:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 档位颜色样式 - QPT回购按钮 */
.qpt-buyback .action-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  color: white;
}

.qpt-buyback .join-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.qpt-buyback .join-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.qpt-buyback .join-button:disabled {
  background: #ccc !important;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 档位颜色样式 */
.qpt-buyback .join-button.tier-100 {
  background: #007bff !important; /* 蓝色 - 100 QPT */
}

.qpt-buyback .join-button.tier-100:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.qpt-buyback .join-button.tier-200 {
  background: #6f42c1 !important; /* 紫色 - 200 QPT */
}

.qpt-buyback .join-button.tier-200:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);
}

.qpt-buyback .join-button.tier-500 {
  background: #dc3545 !important; /* 红色 - 500 QPT */
}

.qpt-buyback .join-button.tier-500:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.qpt-buyback .join-button.tier-1000 {
  background: #17a2b8 !important; /* 青色 - 1000 QPT */
}

.qpt-buyback .join-button.tier-1000:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .qpt-buyback-container {
    padding: 16px;
  }

  .rooms-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .room-card {
    padding: 16px;
  }

  .qpt-buyback .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .qpt-buyback .info-value {
    font-size: 20px;
  }

  .page-title {
    font-size: 24px;
  }
}

// src/components/Merchant/OrderManagement/index.jsx
// 商家订单管理组件

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import {
  getMerchantOrders,
  shipOrder,
  updateOrderStatus,
  ORDER_STATUS,
  ORDER_STATUS_TEXT,
  ORDER_STATUS_COLOR
} from '@/apis/orderApi';
import { getProduct } from '@/apis/mallApi';
import { formatAddress } from '@/utils/addressFormatter';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';
import './OrderManagement.css';

const publicClient = createPublicClient({
  chain: bscTestnet,
  transport: http()
});

export default function OrderManagement() {
  const { address: account, isConnected } = useAccount();

  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [showShippingModal, setShowShippingModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [shippingForm, setShippingForm] = useState({
    trackingNumber: '',
    shippingCompany: ''
  });
  const [isShipping, setIsShipping] = useState(false);
  const [buyerAddresses, setBuyerAddresses] = useState({}); // 存储买家地址信息

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalOrders, setTotalOrders] = useState(0);

  // 获取买家收货地址
  const getBuyerAddress = async (buyerAddress, addressId) => {
    const key = `${buyerAddress}-${addressId}`;

    // 如果已经缓存了，直接返回
    if (buyerAddresses[key]) {
      return buyerAddresses[key];
    }

    try {
      // 使用修复后的 validateUserAddress 函数
      const [exists, addressInfo] = await publicClient.readContract({
        address: CONTRACT_ADDRESSES[97].ProductManagement,
        abi: ABIS.ProductManagement,
        functionName: 'validateUserAddress',
        args: [buyerAddress, BigInt(addressId)]
      });

      if (exists && addressInfo) {
        const formattedAddress = {
          name: addressInfo.name,
          phone: addressInfo.phone,
          fullAddress: `${addressInfo.province} ${addressInfo.city} ${addressInfo.district} ${addressInfo.detail}`,
          province: addressInfo.province,
          city: addressInfo.city,
          district: addressInfo.district,
          detail: addressInfo.detail
        };

        // 缓存地址信息
        setBuyerAddresses(prev => ({
          ...prev,
          [key]: formattedAddress
        }));

        return formattedAddress;
      } else {
        // 如果 validateUserAddress 失败，尝试使用 getUserDefaultAddress 作为备选方案
        try {
          const [hasDefault, defaultAddress] = await publicClient.readContract({
            address: CONTRACT_ADDRESSES[97].ProductManagement,
            abi: ABIS.ProductManagement,
            functionName: 'getUserDefaultAddress',
            args: [buyerAddress]
          });

          if (hasDefault && defaultAddress && Number(defaultAddress.addressId) === Number(addressId)) {
            const formattedAddress = {
              name: defaultAddress.name,
              phone: defaultAddress.phone,
              fullAddress: `${defaultAddress.province} ${defaultAddress.city} ${defaultAddress.district} ${defaultAddress.detail}`,
              province: defaultAddress.province,
              city: defaultAddress.city,
              district: defaultAddress.district,
              detail: defaultAddress.detail
            };

            // 缓存地址信息
            setBuyerAddresses(prev => ({
              ...prev,
              [key]: formattedAddress
            }));

            return formattedAddress;
          }
        } catch (fallbackError) {
          // 静默处理备选方案失败
        }

        return null;
      }
    } catch (error) {
      return null;
    }
  };

  // 加载订单列表
  const loadOrders = async () => {
    if (!account || !isConnected) return;

    setIsLoading(true);
    try {
      const orderList = await getMerchantOrders({ merchantAddress: account });

      // 为每个订单获取商品信息和买家地址信息
      const ordersWithProducts = await Promise.all(
        orderList.map(async (order) => {
          try {
            const [product, buyerAddress] = await Promise.all([
              getProduct({ productId: order.productId }),
              getBuyerAddress(order.buyer, order.addressId)
            ]);
            return { ...order, product, buyerAddress };
          } catch (error) {
            console.error('获取订单详细信息失败:', error);
            return { ...order, product: null, buyerAddress: null };
          }
        })
      );

      setOrders(ordersWithProducts);
      setTotalOrders(ordersWithProducts.length);

      // 应用当前过滤器
      applyFilter(ordersWithProducts, statusFilter);

    } catch (error) {
      toast.error('加载订单列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 应用过滤器（内部函数）
  const applyFilter = (orderList, status) => {
    let filtered;
    if (status === 'all') {
      filtered = orderList;
    } else {
      filtered = orderList.filter(order => order.status === parseInt(status));
    }
    setFilteredOrders(filtered);
    setTotalOrders(filtered.length);
    setCurrentPage(1); // 重置到第一页
  };

  // 过滤订单（外部调用）
  const filterOrders = (status) => {
    setStatusFilter(status);
    applyFilter(orders, status);
  };

  // 分页控制函数
  const totalPages = Math.ceil(totalOrders / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // 打开发货弹窗
  const openShippingModal = (order) => {
    setSelectedOrder(order);
    setShippingForm({
      trackingNumber: '',
      shippingCompany: ''
    });
    setShowShippingModal(true);
  };

  // 处理发货
  const handleShipOrder = async () => {
    if (!selectedOrder) return;

    if (!shippingForm.trackingNumber.trim()) {
      toast.error('请输入快递单号');
      return;
    }

    if (!shippingForm.shippingCompany.trim()) {
      toast.error('请选择快递公司');
      return;
    }

    setIsShipping(true);
    try {
      console.log('🚀 [OrderManagement] 开始发货:', {
        orderId: selectedOrder.orderId,
        trackingNumber: shippingForm.trackingNumber,
        shippingCompany: shippingForm.shippingCompany
      });

      const signer = { address: account };
      
      await shipOrder({
        orderId: selectedOrder.orderId,
        trackingNumber: shippingForm.trackingNumber.trim(),
        shippingCompany: shippingForm.shippingCompany.trim(),
        signer
      });

      toast.success('发货成功！');
      setShowShippingModal(false);
      setSelectedOrder(null);
      
      // 重新加载订单列表
      await loadOrders();

    } catch (error) {
      console.error('❌ [OrderManagement] 发货失败:', error);
      toast.error(`发货失败: ${error.message}`);
    } finally {
      setIsShipping(false);
    }
  };

  // 更新订单状态
  const handleUpdateStatus = async (orderId, newStatus) => {
    try {
      console.log('🔄 [OrderManagement] 更新订单状态:', { orderId, newStatus });

      const signer = { address: account };
      
      await updateOrderStatus({
        orderId,
        newStatus,
        signer
      });

      toast.success('订单状态更新成功！');
      
      // 重新加载订单列表
      await loadOrders();

    } catch (error) {
      console.error('❌ [OrderManagement] 状态更新失败:', error);
      toast.error(`状态更新失败: ${error.message}`);
    }
  };

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp || timestamp === 0) return '-';
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  // 格式化价格
  const formatPrice = (price) => {
    return (price / 1000000).toFixed(2);
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadOrders();
  }, [account, isConnected]);

  if (!account || !isConnected) {
    return (
      <div className="order-management">
        <div className="not-connected">
          <h3>请先连接钱包</h3>
          <p>连接钱包后即可查看订单管理</p>
        </div>
      </div>
    );
  }

  // 获取新订单提示
  const getNewOrdersAlert = () => {
    const pendingOrders = orders.filter(order => order.status === ORDER_STATUS.PENDING);
    if (pendingOrders.length === 0) return null;

    return (
      <div className="new-orders-alert">
        <div className="alert-icon">🔔</div>
        <div className="alert-content">
          <div className="alert-title">有新订单待处理</div>
          <div className="alert-orders">
            {pendingOrders.slice(0, 3).map(order => (
              <span key={order.orderId} className="order-number">
                #{order.orderId}
              </span>
            ))}
            {pendingOrders.length > 3 && (
              <span className="more-orders">等{pendingOrders.length}个订单</span>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="order-management">
      <div className="section-header">
        <div className="header-content">
          <h3>📦 订单管理</h3>
          <p>管理您的商品订单和发货信息</p>
        </div>
        {getNewOrdersAlert()}
      </div>

      {/* 状态过滤器 */}
      <div className="status-filters">
        <button
          className={`filter-btn ${statusFilter === 'all' ? 'active' : ''}`}
          onClick={() => filterOrders('all')}
        >
          全部订单
        </button>
        <button
          className={`filter-btn ${statusFilter === ORDER_STATUS.PENDING.toString() ? 'active' : ''}`}
          onClick={() => filterOrders(ORDER_STATUS.PENDING.toString())}
        >
          待发货
        </button>
        <button
          className={`filter-btn ${statusFilter === ORDER_STATUS.SHIPPED.toString() ? 'active' : ''}`}
          onClick={() => filterOrders(ORDER_STATUS.SHIPPED.toString())}
        >
          已发货
        </button>
      </div>

      {/* 订单列表 */}
      <div className="orders-content">
        {isLoading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>正在加载订单...</p>
          </div>
        ) : filteredOrders.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📦</div>
            <h4>暂无订单</h4>
            <p>当前没有{statusFilter === 'all' ? '' : ORDER_STATUS_TEXT[parseInt(statusFilter)] || ''}订单</p>
          </div>
        ) : (
          <div className="orders-list">
            {paginatedOrders.map((order) => (
              <div key={order.orderId} className="order-card">
                <div className="order-header">
                  <div className="order-info">
                    <span className="order-id">订单号: #{order.orderId}</span>
                    <span 
                      className="order-status"
                      style={{ color: ORDER_STATUS_COLOR[order.status] }}
                    >
                      {ORDER_STATUS_TEXT[order.status]}
                    </span>
                  </div>
                  <div className="order-time">
                    下单时间: {formatTime(order.createTime)}
                  </div>
                </div>

                <div className="order-content">
                  <div className="product-info">
                    <h4>{order.product?.name || '商品信息加载失败'}</h4>
                    <p>数量: {order.quantity} | 单价: {formatPrice(order.totalPrice / order.quantity)} 积分</p>
                    <p className="total-price">总价: {formatPrice(order.totalPrice)} 积分</p>
                  </div>

                  <div className="buyer-info">
                    <p><strong>买家:</strong> {formatAddress(order.buyer)}</p>
                    <p><strong>地址ID:</strong> {order.addressId}</p>
                    {order.buyerAddress && (
                      <div className="shipping-address">
                        <p><strong>收货人:</strong> {order.buyerAddress.name} {order.buyerAddress.phone}</p>
                        <p><strong>收货地址:</strong> {order.buyerAddress.fullAddress}</p>
                      </div>
                    )}
                  </div>

                  {order.status >= ORDER_STATUS.SHIPPED && (
                    <div className="shipping-info">
                      <p><strong>快递公司:</strong> {order.shippingCompany}</p>
                      <p><strong>快递单号:</strong> {order.trackingNumber}</p>
                      <p><strong>发货时间:</strong> {formatTime(order.shippedTime)}</p>
                    </div>
                  )}
                </div>

                <div className="order-actions">
                  {order.status === ORDER_STATUS.PENDING && (
                    <button
                      className="ship-btn"
                      onClick={() => openShippingModal(order)}
                    >
                      📦 发货
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 分页组件 */}
        {!isLoading && totalOrders > itemsPerPage && (
          <div className="pagination-container">
            <div className="pagination-info">
              显示第 {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalOrders)} 项，共 {totalOrders} 项
            </div>
            <div className="pagination-controls">
              <button
                className="pagination-btn"
                onClick={handlePrevPage}
                disabled={currentPage === 1}
              >
                ← 上一页
              </button>

              {/* 页码按钮 */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  className={`pagination-btn ${page === currentPage ? 'active' : ''}`}
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </button>
              ))}

              <button
                className="pagination-btn"
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
              >
                下一页 →
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 发货弹窗 */}
      {showShippingModal && selectedOrder && (
        <div className="modal-overlay">
          <div className="shipping-modal">
            <div className="modal-header">
              <h4>📦 订单发货</h4>
              <button 
                className="close-btn"
                onClick={() => setShowShippingModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="order-summary">
                <p><strong>订单号:</strong> #{selectedOrder.orderId}</p>
                <p><strong>商品:</strong> {selectedOrder.product?.name}</p>
                <p><strong>数量:</strong> {selectedOrder.quantity}</p>
              </div>

              <div className="shipping-form">
                <div className="form-group">
                  <label>快递公司 *</label>
                  <select
                    value={shippingForm.shippingCompany}
                    onChange={(e) => setShippingForm(prev => ({ ...prev, shippingCompany: e.target.value }))}
                  >
                    <option value="">请选择快递公司</option>
                    <option value="顺丰速运">顺丰速运</option>
                    <option value="圆通速递">圆通速递</option>
                    <option value="中通快递">中通快递</option>
                    <option value="申通快递">申通快递</option>
                    <option value="韵达速递">韵达速递</option>
                    <option value="百世快递">百世快递</option>
                    <option value="德邦快递">德邦快递</option>
                    <option value="京东物流">京东物流</option>
                    <option value="邮政EMS">邮政EMS</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>快递单号 *</label>
                  <input
                    type="text"
                    placeholder="请输入快递单号"
                    value={shippingForm.trackingNumber}
                    onChange={(e) => setShippingForm(prev => ({ ...prev, trackingNumber: e.target.value }))}
                  />
                </div>
              </div>
            </div>

            <div className="modal-actions">
              <button 
                className="cancel-btn"
                onClick={() => setShowShippingModal(false)}
                disabled={isShipping}
              >
                取消
              </button>
              <button 
                className="confirm-btn"
                onClick={handleShipOrder}
                disabled={isShipping || !shippingForm.trackingNumber.trim() || !shippingForm.shippingCompany.trim()}
              >
                {isShipping ? '⏳ 发货中...' : '✅ 确认发货'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

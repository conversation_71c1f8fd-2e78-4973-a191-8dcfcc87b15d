// src/components/Profile/ReferralRewards.jsx
// 推荐奖励记录组件
import React, { useState, useEffect } from "react";
import { useAccount, useConfig, useWalletClient } from "wagmi";
import { formatAddress } from "@/apis/referralApi";
import { getQPTReward } from "@/utils/rewardMessages";
import "./index.css";

export default function ReferralRewards() {
  const { address: account } = useAccount();
  const config = useConfig();
  const { data: walletClient } = useWalletClient();
  const [rewards, setRewards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalRewards, setTotalRewards] = useState(0);
  const [claimingRewards, setClaimingRewards] = useState(new Set());
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // 每页显示10条记录
  const [totalPages, setTotalPages] = useState(0);
  const [paginatedRewards, setPaginatedRewards] = useState([]);

  useEffect(() => {
    if (account) {
      fetchReferralRewards();
    }
  }, [account]);

  // 当奖励数据或当前页面变化时，更新分页数据
  useEffect(() => {
    updatePaginatedData();
  }, [rewards, currentPage, itemsPerPage]);

  // 更新分页数据
  const updatePaginatedData = () => {
    const total = Math.ceil(rewards.length / itemsPerPage);
    setTotalPages(total);

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginated = rewards.slice(startIndex, endIndex);
    setPaginatedRewards(paginated);
  };

  // 分页控制函数
  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const fetchReferralRewards = async () => {
    try {
      setLoading(true);

      // 查询推荐人奖励记录
      const { getReferrerRewardHistory, getReferrerPendingReward, hasReferrerClaimed } = await import("@/apis/qptLockerApi");
      const rewardHistory = await getReferrerRewardHistory({
        chainId: 97,
        referrerAddress: account,
      });

      // 为每个奖励记录查询详细状态
      const rewardsWithStatus = await Promise.all(
        rewardHistory.map(async (reward) => {
          try {
            // 并行查询待领取奖励和领取状态
            const [pendingReward, isClaimed] = await Promise.all([
              getReferrerPendingReward({
                chainId: 97,
                referrerAddress: account,
                roomId: reward.roomId
              }),
              hasReferrerClaimed({
                chainId: 97,
                referrerAddress: account,
                roomId: reward.roomId
              })
            ]);

            // 确定实际奖励数量
            let actualQptReward;
            let status;

            if (isClaimed) {
              // 已领取：使用合约记录的原始数量
              actualQptReward = reward.qptReward || '0';
              status = 'claimed';
            } else if (parseFloat(pendingReward) > 0) {
              // 待领取：使用待领取数量
              actualQptReward = pendingReward;
              status = 'pending';
            } else {
              // 无奖励或其他状态：使用原始数量
              actualQptReward = reward.qptReward || '0';
              status = reward.status || 'unknown';
            }

            return {
              ...reward,
              actualQptReward,
              pendingReward,
              isClaimed,
              status,
              displayAmount: actualQptReward // 用于显示的数量
            };
          } catch (error) {
            console.warn(`查询房间${reward.roomId}奖励状态失败:`, error);
            // 查询失败时使用原始数据
            return {
              ...reward,
              actualQptReward: reward.qptReward || '0',
              pendingReward: '0',
              isClaimed: false,
              status: reward.status || 'unknown',
              displayAmount: reward.qptReward || '0'
            };
          }
        })
      );

      // 计算累计奖励总数（包括已领取和待领取的）
      const totalClaimed = rewardsWithStatus
        .filter(reward => reward.isClaimed)
        .reduce((sum, reward) => sum + parseFloat(reward.actualQptReward || 0), 0);

      const totalPending = rewardsWithStatus
        .filter(reward => !reward.isClaimed && parseFloat(reward.pendingReward || 0) > 0)
        .reduce((sum, reward) => sum + parseFloat(reward.pendingReward || 0), 0);

      const totalRewards = totalClaimed + totalPending;



      setRewards(rewardsWithStatus);
      setTotalRewards(totalRewards);
    } catch (error) {
      console.error("获取推荐奖励记录失败:", error);
      // 如果查询失败，显示空数据而不是错误
      setRewards([]);
      setTotalRewards(0);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleClaimReward = async (reward) => {
    if (claimingRewards.has(reward.roomId)) return;

    try {
      setClaimingRewards(prev => new Set([...prev, reward.roomId]));

      // 检查钱包客户端是否可用
      if (!walletClient) {
        throw new Error('钱包未连接，请先连接钱包');
      }

      const { CONTRACT_ADDRESSES, ABIS } = await import('@/contracts');
      const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker;

      // 🎯 优化：直接使用 walletClient 发送交易，确保钱包自动弹窗
      const txHash = await walletClient.writeContract({
        address: qptLockerAddress,
        abi: ABIS.QPTLocker,
        functionName: 'claimReferrerReward',
        args: [BigInt(reward.roomId)]
      });

      // 等待交易确认 - 使用 viem 的公共客户端
      const { createPublicClient, http } = await import('viem');
      const { bscTestnet } = await import('viem/chains');

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash: txHash,
        timeout: 60000
      });

      if (receipt.status === 'success') {
        // 显示成功消息
        setSuccessMessage(`🎉 成功领取 ${reward.qptReward} QPT 推荐奖励！`);

        // 立即刷新奖励列表和QPT余额
        await fetchReferralRewards();

        // 多种方式确保余额更新
        try {
          if (window.refreshTokenBalances) {
            window.refreshTokenBalances();
          }
          if (window.refreshQPTBalance) {
            window.refreshQPTBalance();
          }
          if (window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('qptBalanceUpdate'));
            window.dispatchEvent(new CustomEvent('balanceUpdate', {
              detail: { type: 'qpt', action: 'referral' }
            }));
          }
          // 延迟刷新确保交易已被索引
          setTimeout(() => {
            if (window.refreshTokenBalances) window.refreshTokenBalances();
            if (window.refreshQPTBalance) window.refreshQPTBalance();
          }, 3000);
        } catch (refreshError) {
          console.warn('余额刷新失败:', refreshError);
        }

        // 3秒后清除成功消息
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      } else {
        throw new Error('交易执行失败');
      }
    } catch (error) {
      console.error('领取推荐奖励失败:', error);

      let errorMessage = '领取推荐奖励失败';
      if (error.message.includes('User rejected')) {
        errorMessage = '用户取消了交易';
      } else if (error.message.includes('insufficient funds')) {
        errorMessage = 'BNB余额不足，无法支付Gas费用';
      } else if (error.message.includes('Already claimed')) {
        errorMessage = '推荐奖励已经领取过了';
      } else if (error.message.includes('No reward available')) {
        errorMessage = '没有可领取的推荐奖励';
      } else if (error.message.includes('execution reverted')) {
        errorMessage = '合约执行失败，请检查条件是否满足';
      }



      // 显示错误消息
      setErrorMessage(`❌ 领取失败: ${errorMessage}`);

      // 3秒后清除错误消息
      setTimeout(() => {
        setErrorMessage('');
      }, 3000);
    } finally {
      setClaimingRewards(prev => {
        const newSet = new Set(prev);
        newSet.delete(reward.roomId);
        return newSet;
      });
    }
  };

  if (!account) {
    return (
      <div className="referral-rewards-container">
        <div className="empty-state">
          <p>请先连接钱包</p>
        </div>
      </div>
    );
  }

  return (
    <div className="referral-rewards-container">
      {/* 标题和QPT统计卡片横向布局 */}
      <div className="rewards-header-section">
        <h2 className="profile-title">推荐奖励</h2>
        <div className="total-rewards-card">
          <div className="rewards-value">{totalRewards} QPT</div>
          <div className="rewards-label">累计获得</div>
        </div>
      </div>

      {/* 成功/错误消息提示 */}
      {successMessage && (
        <div className="message-toast success-toast">
          {successMessage}
        </div>
      )}
      {errorMessage && (
        <div className="message-toast error-toast">
          {errorMessage}
        </div>
      )}

      {loading ? (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>加载中...</p>
        </div>
      ) : rewards.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">🎯</div>
          <h4>暂无推荐奖励</h4>
          <p>当您推荐的好友在拼团中获胜时，您可以手动领取相同的QPT奖励</p>
          <p>推荐奖励需要手动领取，请关注拼团结果通知</p>
        </div>
      ) : (
        <>
          <div className="rewards-list">
            {paginatedRewards.map((reward) => (
              <div key={reward.id} className="reward-item">
                <div className="reward-main">
                  <div className="reward-info">
                    <div className="room-info">
                      <span className="room-id">房间 #{reward.roomId}</span>
                      <span className={`tier-amount ${reward.status}`}>
                        {reward.status === "pending"
                          ? "待领取"
                          : reward.status === "claimed"
                            ? "已领取"
                            : "推荐奖励"}
                      </span>
                    </div>
                    <div className="winner-info">
                      <span className="winner-label">获奖好友：</span>
                      <span className="winner-name">
                        {reward.winner && reward.winner !== "N/A"
                          ? `${reward.winner.slice(0, 6)}...${reward.winner.slice(-4)}`
                          : "已领取记录"}
                      </span>
                    </div>
                  </div>

                  <div className="reward-amount">
                    <div className="qpt-amount">
                      +{parseFloat(reward.displayAmount || reward.actualQptReward || reward.qptReward || 0).toFixed(1)} QPT
                    </div>
                    <div className="reward-time">
                      {formatTime(reward.timestamp)}
                    </div>
                    {reward.isClaimed && (
                      <div className="claimed-badge">✅ 已领取</div>
                    )}
                  </div>
                </div>

                {/* 奖励详情 */}
                <div className="reward-details">
                  <div className="detail-item">
                    <div className="detail-label">房间档位</div>
                    <div className="detail-value">{reward.tierAmount || 'N/A'} USDT</div>
                  </div>
                  <div className="detail-item">
                    <div className="detail-label">QPT奖励</div>
                    <div className="detail-value qpt-amount">
                      {parseFloat(reward.displayAmount || reward.actualQptReward || reward.qptReward || 0).toFixed(1)} QPT
                    </div>
                  </div>
                  <div className="detail-item">
                    <div className="detail-label">状态</div>
                    <div className={`detail-value status-${reward.status}`}>
                      {reward.isClaimed ? '已领取' :
                       parseFloat(reward.pendingReward || 0) > 0 ? '待领取' : '无奖励'}
                    </div>
                  </div>
                  <div className="detail-item">
                    <div className="detail-label">时间</div>
                    <div className="detail-value">{formatTime(reward.timestamp)}</div>
                  </div>
                </div>

                {/* 领取按钮 - 只对待领取的奖励显示 */}
                {!reward.isClaimed && parseFloat(reward.pendingReward || 0) > 0 && (
                  <div className="reward-actions">
                    <button
                      className={`claim-reward-btn ${claimingRewards.has(reward.roomId) ? 'claiming' : ''}`}
                      onClick={() => handleClaimReward(reward)}
                      disabled={claimingRewards.has(reward.roomId)}
                    >
                      {claimingRewards.has(reward.roomId)
                        ? '🔄 领取中...'
                        : `🎁 领取 ${parseFloat(reward.pendingReward || 0).toFixed(1)} QPT`
                      }
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* 分页组件 */}
          {totalPages > 1 && (
            <div className="pagination-container">
              <div className="pagination-info">
                显示第 {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, rewards.length)} 条，共 {rewards.length} 条记录
              </div>
              <div className="pagination-controls">
                <button
                  className="pagination-btn prev"
                  onClick={handlePrevPage}
                  disabled={currentPage === 1}
                >
                  上一页
                </button>

                <div className="pagination-numbers">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        className={`pagination-btn number ${currentPage === pageNum ? 'active' : ''}`}
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  className="pagination-btn next"
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </button>
              </div>
            </div>
          )}
        </>
      )}


    </div>
  );
}

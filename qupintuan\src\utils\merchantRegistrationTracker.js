// src/utils/merchantRegistrationTracker.js

/**
 * 商家注册跟踪器
 * 用于管理商家注册状态和地址
 */

const STORAGE_KEY = 'registeredMerchants';

class MerchantRegistrationTracker {
  constructor() {
    this.merchants = new Set();
    this.loadFromStorage();
  }

  /**
   * 从本地存储加载商家地址
   */
  loadFromStorage() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const merchants = JSON.parse(stored);
        if (Array.isArray(merchants)) {
          merchants.forEach(address => {
            if (this.isValidAddress(address)) {
              this.merchants.add(address.toLowerCase());
            }
          });
        }
      }
      // 静默加载商家地址
    } catch (error) {
      console.error('❌ [MerchantTracker] 加载本地存储失败:', error);
    }
  }

  /**
   * 保存到本地存储
   */
  saveToStorage() {
    try {
      const merchantArray = Array.from(this.merchants);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(merchantArray));
      // 静默保存商家地址
    } catch (error) {
      console.error('❌ [MerchantTracker] 保存本地存储失败:', error);
    }
  }

  /**
   * 验证地址格式
   */
  isValidAddress(address) {
    return address &&
           typeof address === 'string' &&
           address.length === 42 &&
           address.startsWith('0x');
  }

  /**
   * 添加商家地址
   */
  addMerchant(address) {
    if (!this.isValidAddress(address)) {
      console.warn('⚠️ [MerchantTracker] 无效的商家地址:', address);
      return false;
    }

    const normalizedAddress = address.toLowerCase();
    if (this.merchants.has(normalizedAddress)) {
      // 商家地址已存在
      return false;
    }

    this.merchants.add(normalizedAddress);
    this.saveToStorage();
    // 静默添加商家地址
    return true;
  }

  /**
   * 移除商家地址
   */
  removeMerchant(address) {
    if (!this.isValidAddress(address)) {
      return false;
    }

    const normalizedAddress = address.toLowerCase();
    if (this.merchants.has(normalizedAddress)) {
      this.merchants.delete(normalizedAddress);
      this.saveToStorage();
      // 静默移除商家地址
      return true;
    }
    return false;
  }

  /**
   * 检查商家是否已注册
   */
  hasMerchant(address) {
    if (!this.isValidAddress(address)) {
      return false;
    }
    return this.merchants.has(address.toLowerCase());
  }

  /**
   * 获取所有商家地址
   */
  getAllMerchants() {
    return Array.from(this.merchants);
  }

  /**
   * 获取商家数量
   */
  getCount() {
    return this.merchants.size;
  }

  /**
   * 清空所有商家
   */
  clear() {
    this.merchants.clear();
    this.saveToStorage();
    // 静默清空所有商家地址
  }

  /**
   * 批量添加商家地址
   */
  addMerchants(addresses) {
    if (!Array.isArray(addresses)) {
      console.warn('⚠️ [MerchantTracker] 批量添加失败: 参数不是数组');
      return 0;
    }

    let addedCount = 0;
    addresses.forEach(address => {
      if (this.addMerchant(address)) {
        addedCount++;
      }
    });

    // 静默批量添加完成
    return addedCount;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalMerchants: this.merchants.size,
      merchants: this.getAllMerchants(),
      lastUpdate: Date.now()
    };
  }
}

// 创建全局实例
const merchantTracker = new MerchantRegistrationTracker();

// 导出实例和类
export { merchantTracker, MerchantRegistrationTracker };

// 开发环境下添加到window对象
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.merchantTracker = merchantTracker;
}

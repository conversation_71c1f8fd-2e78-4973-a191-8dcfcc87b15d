// 简单的 RPC 修复测试脚本
import { createRpcClient, getFastestRpc, testRpcNode } from './src/utils/rpcManager.js';

async function testRpcFix() {
  console.log('🧪 测试 RPC 修复...\n');

  // 测试 RPC 节点
  const testNodes = [
    'https://bsc-testnet.public.blastapi.io',
    'https://data-seed-prebsc-1-s1.binance.org:8545',
    'https://data-seed-prebsc-2-s1.binance.org:8545',
    'https://bsc-testnet.blockpi.network/v1/rpc/public'
  ];

  console.log('📡 测试各个 RPC 节点...');
  for (const node of testNodes) {
    const startTime = Date.now();
    const isAvailable = await testRpcNode(node, 5000);
    const endTime = Date.now();
    
    console.log(`${isAvailable ? '✅' : '❌'} ${node} (${endTime - startTime}ms)`);
  }

  console.log('\n🔍 获取最快的 RPC 节点...');
  try {
    const fastestRpc = await getFastestRpc(97);
    console.log(`✅ 最快节点: ${fastestRpc}`);
  } catch (error) {
    console.error(`❌ 获取失败:`, error.message);
  }

  console.log('\n🔌 创建 RPC 客户端...');
  try {
    const client = await createRpcClient(97, {
      timeout: 8000,
      retryCount: 2,
      retryDelay: 500,
    });
    
    console.log('✅ 客户端创建成功');
    
    // 测试基本功能
    const blockNumber = await client.getBlockNumber();
    console.log(`📦 当前区块号: ${blockNumber}`);
    
  } catch (error) {
    console.error(`❌ 客户端测试失败:`, error.message);
  }

  console.log('\n✨ 测试完成');
}

testRpcFix().catch(console.error);

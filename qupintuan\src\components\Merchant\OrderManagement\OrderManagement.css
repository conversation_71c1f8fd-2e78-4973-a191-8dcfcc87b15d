/* src/components/Merchant/OrderManagement/OrderManagement.css */

.order-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 新订单提示样式 */
.new-orders-alert {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 12px 16px;
  animation: pulse 2s infinite;
}

.new-orders-alert .alert-icon {
  font-size: 20px;
  animation: bounce 1s infinite;
}

.new-orders-alert .alert-content {
  flex: 1;
}

.new-orders-alert .alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 4px;
}

.new-orders-alert .alert-orders {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.new-orders-alert .order-number {
  background: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.new-orders-alert .more-orders {
  color: #92400e;
  font-size: 12px;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-3px); }
  60% { transform: translateY(-2px); }
}

/* 状态过滤器 */
.status-filters {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.filter-btn {
  padding: 10px 20px;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: auto;
  flex-shrink: 0;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

/* 订单内容区域 */
.orders-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.empty-state p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
}

.order-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.order-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.order-id {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.order-status {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(102, 126, 234, 0.1);
}

.order-time {
  color: #666;
  font-size: 14px;
}

.order-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 16px;
}

.product-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.product-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.total-price {
  font-weight: 600;
  color: #667eea !important;
  font-size: 16px !important;
}

.buyer-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.shipping-address {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.shipping-address p {
  margin: 4px 0;
  color: #495057;
  font-size: 14px;
}

.shipping-address p:first-child {
  font-weight: 500;
  color: #333;
}

.shipping-info {
  grid-column: 1 / -1;
  padding: 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 8px;
}

.shipping-info p {
  margin: 4px 0;
  color: #1890ff;
  font-size: 14px;
}

.order-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.ship-btn,
.status-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.ship-btn {
  background: #52c41a;
  color: white;
}

.ship-btn:hover {
  background: #73d13d;
  transform: translateY(-1px);
}

.status-btn {
  background: #1890ff;
  color: white;
}

.status-btn:hover {
  background: #40a9ff;
  transform: translateY(-1px);
}

/* 发货弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.shipping-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h4 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 4px;
}

.close-btn:hover {
  color: #333;
}

.modal-content {
  padding: 20px;
}

.order-summary {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.order-summary p {
  margin: 4px 0;
  color: #333;
  font-size: 14px;
}

.shipping-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group select,
.form-group input {
  padding: 10px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.cancel-btn,
.confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background: #e9ecef;
}

.confirm-btn {
  background: #52c41a;
  color: white;
}

.confirm-btn:hover {
  background: #73d13d;
}

.confirm-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.not-connected {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.not-connected h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 20px;
}

.not-connected p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 24px;
  padding: 20px 0;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  text-align: center;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.pagination-btn:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
  border-color: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-management {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .status-filters {
    flex-wrap: nowrap !important;
    gap: 12px;
    padding: 12px 16px;
  }

  .filter-btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .order-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .order-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .order-actions {
    justify-content: flex-start;
  }

  .shipping-modal {
    width: 95%;
    margin: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .confirm-btn {
    width: 100%;
  }
}

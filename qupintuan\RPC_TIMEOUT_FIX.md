# RPC 超时问题修复说明

## 问题描述

进入个人中心时，控制台输出以下警告信息：

```
[getUserPersonalPerformance] 获取个人业绩失败，使用备用数据: 所有 RPC 节点都失败了。最后一个错误: The request took too long to respond.
```

## 问题原因

1. **RPC节点超时**：`https://bsc-testnet-rpc.publicnode.com` 节点经常超时
2. **缺少超时配置**：`createPublicClient` 没有设置合适的超时时间
3. **RPC节点管理不一致**：代码中硬编码了RPC节点列表
4. **错误处理不够优雅**：超时错误也会输出详细的错误信息

## 修复方案

### 1. 优化 RPC 节点列表

- 移除经常超时的 `https://bsc-testnet-rpc.publicnode.com` 节点
- 调整节点优先级，将更稳定的节点放在前面

### 2. 统一 RPC 管理

- 在 `agentSystemApi.js` 中使用 `rpcManager.js` 的统一管理
- 添加 `createRpcClient` 函数，提供统一的客户端创建接口

### 3. 增强超时配置

```javascript
// 主要查询：10秒超时，重试3次
timeout: 10000,
retryCount: 3,
retryDelay: 1000

// 备用查询：8秒超时，重试2次
timeout: 8000,
retryCount: 2,
retryDelay: 500
```

### 4. 优化错误处理

- 区分超时错误和其他错误
- 减少控制台警告信息的输出
- 缩短备用数据的缓存时间，便于重试

## 修改的文件

### 1. `src/apis/agentSystemApi.js`

- 使用 `createRpcClient` 替代硬编码的客户端创建
- 优化 `getUserPersonalPerformance` 函数的超时处理
- 优化 `getUserPersonalPerformanceSmallRange` 函数
- 改进错误处理逻辑

### 2. `src/utils/rpcManager.js`

- 移除经常超时的 RPC 节点
- 优化 RPC 节点测试函数
- 添加 `createRpcClient` 函数

## 测试验证

创建了测试文件 `src/test/rpcTimeout.test.js` 用于验证修复效果：

```bash
# 运行测试
node src/test/rpcTimeout.test.js
```

## 预期效果

1. **减少超时错误**：通过移除不稳定节点和优化超时配置
2. **更快的响应**：使用更稳定的 RPC 节点
3. **更少的控制台警告**：优化错误处理逻辑
4. **更好的用户体验**：减少加载时间和错误提示

## 后续优化建议

1. **监控 RPC 节点性能**：定期检查各节点的响应时间和成功率
2. **实现智能切换**：根据实时性能自动选择最佳节点
3. **增加缓存策略**：对于不经常变化的数据增加更长的缓存时间
4. **错误上报**：收集 RPC 错误统计，便于进一步优化

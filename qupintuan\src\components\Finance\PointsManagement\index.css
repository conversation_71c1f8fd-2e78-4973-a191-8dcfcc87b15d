/* 积分管理组件样式 */
.points-management {
  padding: 24px;
}

/* 模块头部 */
.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.header-content {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.header-content h3 {
  font-size: 20px;
  font-weight: bold;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-content p {
  font-size: 14px;
  color: #64748b;
  margin: 4px 0 0 0;
}

.refresh-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #475569;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #e2e8f0;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 连接提示 */
.connect-prompt {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

/* 积分余额 */
.points-balance {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.balance-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  border-radius: 12px;
  color: white;
  text-align: center;
}

.balance-item:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.balance-item:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.balance-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.balance-note {
  font-size: 12px;
  opacity: 0.8;
}

/* 转账功能 */
.transfer-section {
  background: #f8fafc;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.transfer-section h4 {
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.transfer-note {
  background: #f0f9ff;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  color: #0369a1;
  font-size: 14px;
  font-weight: 500;
}

.transfer-form {
  margin-bottom: 24px;
}

.input-group {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
}

.input-group input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
}

.input-suffix {
  padding: 12px 16px;
  background: #f9fafb;
  color: #6b7280;
  font-size: 14px;
  border-left: 1px solid #d1d5db;
}

.transfer-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 16px;
}

.transfer-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.transfer-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 转账信息 */
.transfer-info {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.info-item:first-child {
  color: #475569;
}

.info-item:last-child {
  color: #059669;
  font-weight: 500;
}

.info-note {
  font-size: 12px;
  color: #f59e0b;
  margin-top: 12px;
  padding: 8px 12px;
  background: #fffbeb;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-management {
    padding: 16px;
  }

  .module-header {
    /* 保持横向排列，但调整间距 */
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
  }

  /* 标题区域在移动端保持紧凑 */
  .header-content h3 {
    font-size: 18px;
    margin-bottom: 2px;
  }

  .header-content p {
    font-size: 12px;
    margin: 0;
  }

  /* 刷新按钮在移动端稍微小一点 */
  .refresh-btn {
    padding: 6px 12px;
    font-size: 12px;
    flex-shrink: 0;
  }

  .points-balance {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .balance-item {
    padding: 16px;
  }

  .balance-value {
    font-size: 20px;
  }

  .exchange-section {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .points-management {
    padding: 12px;
  }

  .exchange-section {
    padding: 12px;
  }

  .transfer-section {
    padding: 12px;
  }

  .input-group {
    margin-bottom: 16px;
  }

  .input-group input {
    padding: 12px 14px;
    font-size: 16px; /* 保持16px避免iOS缩放 */
    min-height: 44px; /* 确保足够的触摸区域 */
    box-sizing: border-box;
  }

  .input-suffix {
    padding: 12px 14px;
    font-size: 14px;
    min-height: 44px;
    display: flex;
    align-items: center;
    white-space: nowrap;
  }

  /* 优化转账表单在移动端的显示 */
  .transfer-form {
    width: 100%;
  }

  .transfer-form .input-group {
    width: 100%;
    box-sizing: border-box;
  }

  .transfer-form .input-group input {
    width: 100%;
    box-sizing: border-box;
  }

  /* 确保输入框内容完全可见 */
  .transfer-form .input-group input::placeholder {
    font-size: 14px;
    color: #9ca3af;
  }

  .exchange-btn {
    padding: 12px;
    font-size: 14px;
  }
}

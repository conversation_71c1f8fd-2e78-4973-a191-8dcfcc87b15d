// src/components/Admin/MerchantManagement/index.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import {
  getPendingMerchants,
  approveMerchant,
  testMerchantInfo,
  getMerchantStats,
  debugMerchantApplication,
  addMerchantToBlacklist,
  removeMerchantFromBlacklist,
  isMerchantBlacklisted
} from '@/apis/adminApi';
import { getMerchantInfo } from '@/apis/mallApi';
// 已移除调试工具的引用
import { checkAdminPermissions, simulateAdminApproval, getPermissionSolutions } from '@/utils/adminPermissionChecker';
import { formatPoints } from '@/utils/pointsFormatter';
import './index.css';

export default function MerchantManagement() {
  const { address: account } = useAccount();

  // 状态管理
  const [pendingMerchants, setPendingMerchants] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [processingMerchant, setProcessingMerchant] = useState(null);
  const [selectedMerchant, setSelectedMerchant] = useState(null);
  const [merchantDetails, setMerchantDetails] = useState(null);
  const [merchantStats, setMerchantStats] = useState({
    totalMerchants: 0,
    totalProducts: 0,
    pendingMerchants: 0,
    approvedMerchants: 0
  });

  // 黑名单相关状态
  const [blacklistStatus, setBlacklistStatus] = useState({});
  const [processingBlacklist, setProcessingBlacklist] = useState(null);

  // 加载商家统计数据
  const loadMerchantStats = async () => {
    try {
      // 使用新的商家统计API
      const stats = await getMerchantStats();

      setMerchantStats({
        totalMerchants: stats.registeredMerchants || 0, // 使用注册商家数量
        totalProducts: stats.totalProducts || 0,
        pendingMerchants: stats.pendingMerchants || 0,
        approvedMerchants: stats.approvedMerchants || 0
      });
    } catch (error) {
      console.error('❌ [MerchantManagement] 加载商家统计失败:', error);
      // 如果合约查询失败，使用基础数据
      setMerchantStats({
        totalMerchants: 0,
        totalProducts: 0,
        pendingMerchants: pendingMerchants.length,
        approvedMerchants: 0
      });
    }
  };

  // 加载待审核商家列表
  const loadPendingMerchants = async () => {
    setIsLoading(true);
    try {
      const merchants = await getPendingMerchants();
      setPendingMerchants(merchants);

      // 检查每个商家的黑名单状态
      for (const merchant of merchants) {
        if (merchant.address) {
          await checkBlacklistStatus(merchant.address);
        }
      }

      // if (merchants.length === 0) {
      //   console.log('⚠️ [MerchantManagement] 没有找到待审核商家，请检查：');
      //   console.log('1. 商家是否已注册但未激活');
      //   console.log('2. 商家地址是否正确');
      //   console.log('3. 合约地址和ABI是否正确');
      // }

      // 更新统计数据
      await loadMerchantStats();

    } catch (error) {
      console.error('❌ [MerchantManagement] 加载待审核商家失败:', error);
      toast.error(`加载待审核商家失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 查看商家详情
  const viewMerchantDetails = async (merchantAddress) => {
    try {
      setSelectedMerchant(merchantAddress);
      const details = await getMerchantInfo({ merchantAddress });
      setMerchantDetails(details);

      console.log('🔍 [MerchantManagement] 商家详情:', details);

    } catch (error) {
      console.error('❌ [MerchantManagement] 获取商家详情失败:', error);
      toast.error(`获取商家详情失败: ${error.message}`);
    }
  };

  // 测试商家数据
  const testMerchantData = async () => {
    try {
      const testAddress = '0xDf98905098CB4e5D261578f600337eeeFd4082b3';
      console.log('🧪 [MerchantManagement] 开始测试商家数据...');

      // 测试商家基本信息
      const merchantResult = await testMerchantInfo({ merchantAddress: testAddress });
      console.log('✅ [MerchantManagement] 商家基本信息:', merchantResult);

      // 调试申请详细信息
      const applicationResult = await debugMerchantApplication({ merchantAddress: testAddress });
      console.log('🔍 [MerchantManagement] 申请详细信息调试:', applicationResult);

      toast.success(`测试成功！商家: ${merchantResult.name}`, {
        duration: 4000,
        position: 'top-center',
      });

    } catch (error) {
      console.error('❌ [MerchantManagement] 测试失败:', error);
      toast.error(`测试失败: ${error.message}`);
    }
  };

  // 审核通过商家
  const handleApproveMerchant = async (merchantAddress) => {
    setProcessingMerchant(merchantAddress);
    try {
      // 验证钱包连接
      if (!account) {
        throw new Error('请先连接钱包');
      }

      // 简化signer创建，直接使用account地址
      const signer = {
        account: { address: account },
        address: account
      };

      // 首先检查权限
      const permissionCheck = await checkAdminPermissions(account);

      if (!permissionCheck.permissions.merchantManagement?.hasAdminPermission) {
        console.warn('⚠️ [MerchantManagement] 当前用户无直接管理员权限');

        // 在开发环境中提供备选方案
        if (import.meta.env.DEV) {
          const useSimulation = window.confirm(
            '当前地址不是合约管理员。\n\n' +
            '检测到开发环境，是否使用模拟审核？\n' +
            '（这将只更新本地状态，不会调用合约）'
          );

          if (useSimulation) {
            const simulationResult = await simulateAdminApproval(merchantAddress);

            if (simulationResult.success) {
              toast.success('🧪 开发环境模拟审核成功！', {
                duration: 4000,
                position: 'top-center',
              });

              // 重新加载商家列表
              await loadPendingMerchants();
              return;
            } else {
              throw new Error(simulationResult.error);
            }
          }
        }

        // 如果不使用模拟，显示权限错误
        throw new Error('权限不足：当前地址不是合约管理员。请使用有权限的地址或在开发环境中使用模拟审核。');
      }

      // 如果有权限，执行正常的合约调用
      const result = await approveMerchant({
        merchantAddress,
        signer
      });



      // 成功提示
      toast.success('🎉 商家审核通过！', {
        duration: 4000,
        position: 'top-center',
      });

      // 重新加载列表
      await loadPendingMerchants();

    } catch (error) {
      console.error('❌ [MerchantManagement] 商家审核失败:', error);

      // 更详细的错误处理
      let errorMessage = error.message;
      if (error.message.includes('Ownable: caller is not the owner')) {
        errorMessage = '权限不足：当前账户不是合约管理员';
      } else if (error.message.includes('Not a merchant')) {
        errorMessage = '该地址不是已注册的商家';
      } else if (error.message.includes('Already verified')) {
        errorMessage = '该商家已经通过认证';
      } else if (error.message.includes('Merchant is blacklisted')) {
        errorMessage = '该商家在黑名单中，无法审核';
      }

      toast.error(`商家审核失败: ${errorMessage}`);
    } finally {
      setProcessingMerchant(null);
    }
  };

  // 检查商家黑名单状态
  const checkBlacklistStatus = async (merchantAddress) => {
    try {
      const isBlacklisted = await isMerchantBlacklisted({ merchantAddress });
      setBlacklistStatus(prev => ({
        ...prev,
        [merchantAddress]: isBlacklisted
      }));
      return isBlacklisted;
    } catch (error) {
      return false;
    }
  };

  // 添加商家到黑名单
  const handleAddToBlacklist = async (merchantAddress) => {
    if (!account) {
      toast.error('请先连接钱包');
      return;
    }

    const confirmed = window.confirm(`确定要将商家 ${formatAddress(merchantAddress)} 添加到黑名单吗？\n\n🚫 核心限制功能：\n1. 禁止兑换销售积分为USDT\n2. 商品从商城中移除（不再显示）\n\n📋 其他限制：\n- 禁止创建和管理商品\n- 禁止更新商家信息\n- 自动设置商家为非活跃状态\n\n⚠️ 注意：黑名单不影响审核流程`);

    if (!confirmed) return;

    setProcessingBlacklist(merchantAddress);
    try {
      console.log('🚫 [MerchantManagement] 添加商家到黑名单:', merchantAddress);

      // 创建 signer
      const { BrowserProvider } = await import('ethers');
      const provider = new BrowserProvider(window.ethereum);
      const ethersSigner = await provider.getSigner();

      const signer = {
        account: { address: account },
        address: account
      };

      const result = await addMerchantToBlacklist({
        merchantAddress,
        signer
      });

      console.log('✅ [MerchantManagement] 商家已添加到黑名单:', result);

      toast.success(`🚫 商家 ${formatAddress(merchantAddress)} 已添加到黑名单`);

      // 更新黑名单状态
      setBlacklistStatus(prev => ({
        ...prev,
        [merchantAddress]: true
      }));

      // 重新加载商家列表
      await loadPendingMerchants();

    } catch (error) {
      console.error('❌ [MerchantManagement] 添加黑名单失败:', error);

      let errorMessage = error.message;
      if (error.message.includes('Ownable: caller is not the owner')) {
        errorMessage = '权限不足：当前账户不是合约管理员';
      } else if (error.message.includes('Already blacklisted')) {
        errorMessage = '该商家已在黑名单中';
      }

      toast.error(`添加黑名单失败: ${errorMessage}`);
    } finally {
      setProcessingBlacklist(null);
    }
  };

  // 从黑名单移除商家
  const handleRemoveFromBlacklist = async (merchantAddress) => {
    if (!account) {
      toast.error('请先连接钱包');
      return;
    }

    const confirmed = window.confirm(`确定要将商家 ${formatAddress(merchantAddress)} 从黑名单中移除吗？\n\n✅ 将恢复核心功能：\n1. 允许兑换销售积分为USDT\n2. 商品重新在商城中显示\n\n📋 其他恢复权限：\n- 允许创建和管理商品\n- 允许更新商家信息\n- 恢复正常经营活动`);

    if (!confirmed) return;

    setProcessingBlacklist(merchantAddress);
    try {
      console.log('✅ [MerchantManagement] 从黑名单移除商家:', merchantAddress);

      // 创建 signer
      const { BrowserProvider } = await import('ethers');
      const provider = new BrowserProvider(window.ethereum);
      const ethersSigner = await provider.getSigner();

      const signer = {
        account: { address: account },
        address: account
      };

      const result = await removeMerchantFromBlacklist({
        merchantAddress,
        signer
      });

      console.log('✅ [MerchantManagement] 商家已从黑名单移除:', result);

      toast.success(`✅ 商家 ${formatAddress(merchantAddress)} 已从黑名单移除`);

      // 更新黑名单状态
      setBlacklistStatus(prev => ({
        ...prev,
        [merchantAddress]: false
      }));

      // 重新加载商家列表
      await loadPendingMerchants();

    } catch (error) {
      console.error('❌ [MerchantManagement] 移除黑名单失败:', error);

      let errorMessage = error.message;
      if (error.message.includes('Ownable: caller is not the owner')) {
        errorMessage = '权限不足：当前账户不是合约管理员';
      } else if (error.message.includes('Not blacklisted')) {
        errorMessage = '该商家不在黑名单中';
      }

      toast.error(`移除黑名单失败: ${errorMessage}`);
    } finally {
      setProcessingBlacklist(null);
    }
  };

  // 格式化地址显示
  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // 格式化时间显示
  const formatTime = (timestamp) => {
    if (!timestamp) return '暂无数据';

    // 如果是 ISO 字符串格式，直接解析
    if (typeof timestamp === 'string') {
      return new Date(timestamp).toLocaleString('zh-CN');
    }

    // 如果是时间戳（秒），转换为毫秒
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadPendingMerchants();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="merchant-management">
      <div className="management-header">
        <div className="header-content">
          <h2>🏪 商家管理</h2>
          <p>审核和管理平台商家</p>
        </div>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={loadPendingMerchants}
            disabled={isLoading}
          >
            {isLoading ? '🔄 加载中...' : '🔄 刷新列表'}
          </button>
        </div>
      </div>

      {/* 商家统计信息 */}
      <div className="merchant-stats-section">
        <h3>📊 商家统计</h3>
        <div className="stats-grid">
          <div className="stat-card primary">
            <div className="stat-icon">🏪</div>
            <div className="stat-content">
              <div className="stat-value">{merchantStats.totalMerchants}</div>
              <div className="stat-label">注册商家</div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">⏳</div>
            <div className="stat-content">
              <div className="stat-value">{pendingMerchants.length}</div>
              <div className="stat-label">待审核商家</div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">📦</div>
            <div className="stat-content">
              <div className="stat-value">{merchantStats.totalProducts}</div>
              <div className="stat-label">商品总数</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">✅</div>
            <div className="stat-content">
              <div className="stat-value">{merchantStats.approvedMerchants}</div>
              <div className="stat-label">已审核商家</div>
            </div>
          </div>
        </div>
      </div>

      {/* 商家列表 */}
      <div className="merchant-list-container">
        {isLoading ? (
          <div className="loading-container">
            <div className="loading-spinner">🔄</div>
            <p>正在加载商家列表...</p>
          </div>
        ) : pendingMerchants.length === 0 ? (
          <div className="empty-container">
            <div className="empty-icon">🏪</div>
            <h3>暂无待审核商家</h3>
            <p>目前没有需要审核的商家申请</p>
          </div>
        ) : (
          <div className="merchant-list">
            {pendingMerchants.map((merchant, index) => (
              <div key={`${merchant.address}-${index}`} className="merchant-card">
                <div className="merchant-header">
                  <div className="merchant-info">
                    <h4 className="merchant-name">{merchant.name || '未知商家'}</h4>
                    <span className="merchant-address">{formatAddress(merchant.address)}</span>
                  </div>
                  <div className="merchant-status">
                    <span className="status-badge pending">⏳ 待审核</span>
                    {blacklistStatus[merchant.address] && (
                      <span className="status-badge blacklisted">🚫 黑名单</span>
                    )}
                  </div>
                </div>

                <div className="merchant-description">
                  <p>{merchant.description || '商家身份已注册，详细信息将在审核申请时补充'}</p>
                </div>

                {/* 显示申请详细信息 */}
                <div className="merchant-details">
                  <div className="details-section">
                    <h5>📞 联系方式：</h5>
                    <p>电话：{merchant.applicationDetails?.contactPhone || '未填写'}</p>
                    <p>邮箱：{merchant.applicationDetails?.contactEmail || '未填写'}</p>
                  </div>
                  <div className="details-section">
                    <h5>📍 经营信息：</h5>
                    <p>地址：{merchant.applicationDetails?.address || '未填写'}</p>
                    <p>类目：{merchant.applicationDetails?.category || 'other'}</p>
                    <p>执照：{merchant.applicationDetails?.businessLicense ? '已上传' : '未上传'}</p>
                  </div>

                  {/* 开发环境调试信息 - 仅在有问题时显示 */}
                  {process.env.NODE_ENV === 'development' && !merchant.applicationDetails && (
                    <div style={{
                      fontSize: '12px',
                      color: '#dc3545',
                      marginTop: '10px',
                      padding: '8px',
                      backgroundColor: '#f8d7da',
                      borderRadius: '4px',
                      border: '1px solid #f5c6cb'
                    }}>
                      <strong>⚠️ 调试警告:</strong> 申请详细信息缺失
                    </div>
                  )}
                </div>

                <div className="merchant-meta">
                  <div className="meta-item">
                    <span className="meta-label">申请时间:</span>
                    <span className="meta-value">
                      {merchant.applicationDetails?.submitTime ?
                        formatTime(merchant.applicationDetails.submitTime) :
                        '暂无数据'
                      }
                    </span>
                  </div>
                  <div className="meta-item">
                    <span className="meta-label">Logo:</span>
                    <span className="meta-value">{merchant.logo ? '已上传' : '未上传'}</span>
                  </div>
                </div>

                <div className="merchant-actions">
                  <button
                    className="action-btn view"
                    onClick={() => viewMerchantDetails(merchant.address)}
                  >
                    🔍 查看详情
                  </button>
                  <button
                    className="action-btn approve"
                    onClick={() => handleApproveMerchant(merchant.address)}
                    disabled={processingMerchant === merchant.address}
                  >
                    {processingMerchant === merchant.address ?
                      '⏳ 处理中...' :
                      '✅ 审核通过'
                    }
                  </button>
                  <button className="action-btn reject">
                    ❌ 拒绝申请
                  </button>

                  {/* 黑名单管理按钮 */}
                  {blacklistStatus[merchant.address] ? (
                    <button
                      className="action-btn unblacklist"
                      onClick={() => handleRemoveFromBlacklist(merchant.address)}
                      disabled={processingBlacklist === merchant.address}
                      title="从黑名单中移除该商家"
                    >
                      {processingBlacklist === merchant.address ?
                        '⏳ 处理中...' :
                        '✅ 解除限制'
                      }
                    </button>
                  ) : (
                    <button
                      className="action-btn blacklist"
                      onClick={() => handleAddToBlacklist(merchant.address)}
                      disabled={processingBlacklist === merchant.address}
                      title="将该商家添加到黑名单"
                    >
                      {processingBlacklist === merchant.address ?
                        '⏳ 处理中...' :
                        '🚫 加入黑名单'
                      }
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 商家详情弹窗 */}
      {selectedMerchant && merchantDetails && (
        <div className="merchant-modal-overlay" onClick={() => setSelectedMerchant(null)}>
          <div className="merchant-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>商家详情</h3>
              <button
                className="close-btn"
                onClick={() => setSelectedMerchant(null)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="detail-section">
                <h4>基本信息</h4>
                <div className="detail-grid">
                  <div className="detail-item">
                    <span className="detail-label">商家名称:</span>
                    <span className="detail-value">{merchantDetails.name}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">商家地址:</span>
                    <span className="detail-value">{selectedMerchant}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">商家描述:</span>
                    <span className="detail-value">{merchantDetails.description}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">申请时间:</span>
                    <span className="detail-value">
                      {merchantDetails.applicationDetails?.submitTime ?
                        formatTime(merchantDetails.applicationDetails.submitTime) :
                        formatTime(merchantDetails.createTime)
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* 申请详细信息 */}
              {merchantDetails.applicationDetails && (
                <div className="detail-section">
                  <h4>申请详细信息</h4>
                  <div className="detail-grid">
                    <div className="detail-item">
                      <span className="detail-label">联系电话:</span>
                      <span className="detail-value">
                        {merchantDetails.applicationDetails.contactPhone || '未填写'}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">联系邮箱:</span>
                      <span className="detail-value">
                        {merchantDetails.applicationDetails.contactEmail || '未填写'}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">经营地址:</span>
                      <span className="detail-value">
                        {merchantDetails.applicationDetails.address || '未填写'}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">经营类目:</span>
                      <span className="detail-value">
                        {merchantDetails.applicationDetails.category || 'other'}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">营业执照:</span>
                      <span className="detail-value">
                        {merchantDetails.applicationDetails.businessLicense ?
                          <a href={merchantDetails.applicationDetails.businessLicense} target="_blank" rel="noopener noreferrer">
                            查看执照
                          </a> :
                          '未上传'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div className="detail-section">
                <h4>经营数据</h4>
                <div className="detail-grid">
                  <div className="detail-item">
                    <span className="detail-label">总订单数:</span>
                    <span className="detail-value">{merchantDetails.totalOrders} 单</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">总积分:</span>
                    <span className="detail-value">{formatPoints(merchantDetails.totalPoints)} 分</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">已兑换积分:</span>
                    <span className="detail-value">{formatPoints(merchantDetails.exchangedPoints)} 分</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">状态:</span>
                    <span className={`detail-value ${merchantDetails.isActive ? 'active' : 'inactive'}`}>
                      {merchantDetails.isActive ? '✅ 已激活' : '⏳ 待激活'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-actions">
              <button
                className="modal-btn approve"
                onClick={() => {
                  handleApproveMerchant(selectedMerchant);
                  setSelectedMerchant(null);
                }}
                disabled={processingMerchant === selectedMerchant}
              >
                ✅ 审核通过
              </button>
              <button className="modal-btn reject">
                ❌ 拒绝申请
              </button>
              <button
                className="modal-btn cancel"
                onClick={() => setSelectedMerchant(null)}
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 快速操作 */}
      <div className="quick-actions">
        <h3>⚡ 快速操作</h3>
        <div className="action-buttons">
          <button className="action-btn test" onClick={testMerchantData}>
            🧪 测试商家数据
          </button>
          {/* 调试按钮已移除 */}
          <button
            className="action-btn warning"
            onClick={async () => {
              try {
                const permissionCheck = await checkAdminPermissions(account);
                const solutions = await getPermissionSolutions(account);



                if (permissionCheck.permissions.merchantManagement?.hasAdminPermission) {
                  const permType = permissionCheck.permissions.merchantManagement.isSystemAdmin ? '系统管理员' : 'owner';
                  toast.success(`✅ 当前地址具有管理员权限 (${permType})`);
                } else {
                  toast.error('❌ 当前地址无管理员权限，请查看控制台获取解决方案');
                }
              } catch (error) {
                toast.error(`权限检查失败: ${error.message}`);
              }
            }}
          >
            🔐 检查管理员权限
          </button>
          <button className="action-btn info">
            📦 商品管理
          </button>
        </div>
      </div>
    </div>
  );
}

/* 财务页面样式 */
.finance-home {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 110px);
}

/* 页面头部 */
.finance-header {
  text-align: left;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.finance-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.finance-header p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

/* 移除重复的钱包状态样式，顶部导航栏已有 */

/* 财务模块容器 */
.finance-modules {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.module-section {
  background: transparent;
  border-radius: 12px;
  box-shadow: none;
  overflow: visible;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.module-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .finance-home {
    padding: 16px;
  }

  .finance-header {
    padding: 16px;
    margin-bottom: 20px;
  }

  .finance-header h1 {
    font-size: 24px;
  }

  .finance-header p {
    font-size: 14px;
  }

  /* 移除钱包状态响应式样式 */

  .finance-modules {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .finance-home {
    padding: 12px;
  }

  .finance-header {
    padding: 12px;
  }

  .finance-header h1 {
    font-size: 20px;
  }

  /* 移除钱包状态小屏样式 */
}

/* NodeStaking 简化版样式 */
.node-staking-simplified {
  max-width: 800px !important;
  margin: 0 auto !important;
  padding: 20px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 16px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  color: white !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 模块头部 */
.node-staking-simplified .module-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: 30px !important;
  position: relative !important;
  visibility: visible !important;
}

.header-content {
  flex: 1;
  text-align: center;
  min-width: 0;
}

.header-content h3 {
  font-size: 28px;
  margin: 0 0 10px 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-content p {
  font-size: 16px;
  margin: 0 0 20px 0;
  opacity: 0.9;
}

.refresh-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 节点状态卡片 */
.node-status-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  font-size: 32px;
  margin-right: 20px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
}

.status-desc {
  font-size: 14px;
  opacity: 0.8;
}

/* 质押数量显示 */
.staking-amount {
  margin-left: auto;
  text-align: right;
  padding: 12px 16px;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

.staking-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.staking-value {
  font-size: 16px;
  font-weight: 700;
  color: #3b82f6;
}

/* 简化的分红信息 */
.dividend-info-simplified {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dividend-info-simplified h4 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
}

.dividend-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.dividend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.4) !important;
  padding: 15px 20px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(5px);
}

.dividend-item.highlight {
  background: rgba(255, 215, 0, 0.3) !important;
  border-color: rgba(255, 215, 0, 0.6) !important;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
}

.dividend-item .label {
  font-size: 14px;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9) !important;
}

.dividend-item .value {
  font-size: 16px;
  font-weight: 600;
  color: white !important;
}

.dividend-item.highlight .value {
  color: #ffd700;
  font-size: 18px;
}

/* 分红计算状态 */
.calculate-status {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.calculate-status h4 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
}

.calculate-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-size: 14px;
  opacity: 0.8;
}

.info-item .value {
  font-size: 14px;
  font-weight: 500;
}

.value.can-calculate {
  color: #4ade80;
}

.value.cannot-calculate {
  color: #f87171;
}

.calculate-btn {
  width: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.calculate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.calculate-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  opacity: 0.6;
}

/* 操作按钮 */
.action-buttons {
  margin-bottom: 30px;
}

.stake-btn {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  color: white;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
}

.stake-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  opacity: 0.6;
}

.active-node-actions {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 15px;
}

.claim-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.claim-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
}

.claim-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  opacity: 0.6;
}

.unstake-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  color: white;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.unstake-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(239, 68, 68, 0.4);
}

.unstake-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  opacity: 0.6;
}

/* 简化的规则说明 */
.simplified-rules {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.simplified-rules h4 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
}

.rules-content {
  display: grid;
  gap: 15px;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.rule-title {
  font-size: 14px;
  font-weight: 600;
  min-width: 100px;
  color: #ffd700;
}

.rule-desc {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-staking-simplified {
    padding: 15px;
    margin: 10px;
  }

  .module-header {
    /* 保持横向排列，但调整间距 */
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
  }

  .header-content {
    text-align: left; /* 移动端左对齐 */
  }

  .header-content h3 {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .header-content p {
    font-size: 14px;
    margin: 0;
  }

  .refresh-btn {
    padding: 6px 12px;
    font-size: 12px;
    flex-shrink: 0;
  }
  
  .dividend-grid {
    grid-template-columns: 1fr;
  }
  
  .active-node-actions {
    grid-template-columns: 1fr;
  }
  
  .rule-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .rule-title {
    min-width: auto;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.calculating {
  animation: pulse 2s infinite;
}

/* 用户质押信息样式 */
.user-staking-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.user-staking-info h4 {
  color: white;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.staking-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.staking-item {
  padding: 16px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
}

.staking-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.staking-item .label {
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.staking-item .value {
  font-size: 18px;
  font-weight: 700;
}

/* 计算提示样式 */
.calculate-hint {
  margin-top: 16px;
  padding: 12px 16px;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-icon {
  font-size: 16px;
}

.hint-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .staking-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .staking-item {
    padding: 12px;
  }

  .staking-item .value {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .staking-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .user-staking-info {
    padding: 20px;
  }

  .calculate-hint {
    padding: 10px 12px;
  }

  .hint-text {
    font-size: 13px;
  }
}

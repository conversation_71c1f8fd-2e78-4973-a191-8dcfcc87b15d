// src/components/Merchant/SalesPointsManagement/index.jsx
import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { toast } from 'react-hot-toast'
import { formatUnits } from 'ethers'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { createPublicClient, createWalletClient, custom, http, decodeEventLog } from 'viem'
import { bscTestnet } from 'viem/chains'
import {
  formatPoints,
  parsePointsInput,
  validatePointsInput,
  calculatePointsToUSDT,
  POINTS_DECIMALS
} from '@/utils/pointsFormatter'
import './index.css'

export default function SalesPointsManagement() {
  const { address: account, isConnected } = useAccount()

  // 状态管理
  const [pointsData, setPointsData] = useState({
    salesPoints: '0',
    totalExchanged: '0',
    lastExchangeTime: 0
  })
  const [exchangeAmount, setExchangeAmount] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isExchanging, setIsExchanging] = useState(false)

  // 创建客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  })

  // 加载积分数据
  const loadPointsData = async () => {
    if (!account || !isConnected) return

    setIsLoading(true)
    try {
      const pointsAddress = CONTRACT_ADDRESSES[97].PointsManagement

      // 并行查询所有数据
      const [salesPoints, lastExchange, userExchangeRecords] = await Promise.all([
        publicClient.readContract({
          address: pointsAddress,
          abi: ABIS.PointsManagement,
          functionName: 'salesPointsExchangeable',
          args: [account]
        }),
        publicClient.readContract({
          address: pointsAddress,
          abi: ABIS.PointsManagement,
          functionName: 'lastExchangeTime',
          args: [account]
        }),
        // 获取用户的兑换记录数量
        publicClient.readContract({
          address: pointsAddress,
          abi: ABIS.PointsManagement,
          functionName: 'getExchangeRecordsCount',
          args: [account]
        }).catch(() => 0n) // 如果方法不存在，返回0
      ])

      // 计算用户累计兑换的USDT
      let totalUserExchanged = 0;
      try {
        const recordCount = Number(userExchangeRecords);
        if (recordCount > 0) {
          // 获取所有兑换记录并累计USDT
          const exchangePromises = [];
          for (let i = 0; i < recordCount; i++) {
            exchangePromises.push(
              publicClient.readContract({
                address: pointsAddress,
                abi: ABIS.PointsManagement,
                functionName: 'getExchangeRecord',
                args: [account, BigInt(i)]
              }).catch(() => null)
            );
          }

          const records = await Promise.all(exchangePromises);
          totalUserExchanged = records
            .filter(record => record && record[3]) // record[3] 是 isExchanged
            .reduce((sum, record) => sum + Number(formatUnits(record[1], 6)), 0); // record[1] 是 usdtAmount
        }
      } catch (error) {
        console.log('获取用户兑换记录失败，使用默认值0:', error);
        totalUserExchanged = 0;
      }

      setPointsData({
        salesPoints: formatPoints(salesPoints || 0),
        totalExchanged: totalUserExchanged.toFixed(2),
        lastExchangeTime: lastExchange ? Number(lastExchange) : 0,
        rawSalesPoints: salesPoints || 0n
      })
    } catch (error) {
      console.error('加载积分数据失败:', error)
      toast.error('加载积分数据失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 兑换积分为USDT
  const handleExchange = async () => {
    if (!account || !exchangeAmount) {
      return;
    }

    // 检查用户是否已注册代理系统
    try {
      const { checkUserRegistered } = await import('@/apis/agentSystemApi');
      const isRegistered = await checkUserRegistered({ userAddress: account });

      if (!isRegistered) {
        toast.error('请先注册代理系统才能兑换销售积分！', {
          duration: 5000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('检查注册状态失败:', error);
      toast.error('检查注册状态失败，请重试');
      return;
    }

    // 检查用户是否为已认证的商家
    try {
      const { isMerchant } = await import('@/apis/mallApi');
      const merchantStatus = await isMerchant({ userAddress: account });

      if (!merchantStatus.isVerified) {
        toast.error('只有已认证的商家才能兑换销售积分！请先申请并通过商家认证。', {
          duration: 5000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('检查商家状态失败:', error);
      toast.error('检查商家状态失败，请重试');
      return;
    }

    // 检查商家是否在黑名单中
    try {
      const { isMerchantBlacklisted } = await import('@/apis/adminApi');
      const isBlacklisted = await isMerchantBlacklisted({ merchantAddress: account });

      if (isBlacklisted) {
        toast.error('您的账户已被限制，无法兑换销售积分。如有疑问请联系客服。', {
          duration: 5000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('检查黑名单状态失败:', error);
      toast.error('检查账户状态失败，请重试');
      return;
    }

    // 验证输入格式
    const validation = validatePointsInput(exchangeAmount);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    // 解析输入的积分数量
    const amountInWei = parsePointsInput(exchangeAmount);
    const availableInWei = pointsData.rawSalesPoints || 0n;

    if (amountInWei > availableInWei) {
      toast.error('可兑换积分不足')
      return
    }

    // 检查冷却时间（24小时）
    const now = Math.floor(Date.now() / 1000)
    const cooldown = 24 * 60 * 60 // 24小时

    if (pointsData.lastExchangeTime > 0 && (now - pointsData.lastExchangeTime) < cooldown) {
      const remainingTime = cooldown - (now - pointsData.lastExchangeTime)
      const hours = Math.floor(remainingTime / 3600)
      const minutes = Math.floor((remainingTime % 3600) / 60)
      toast.error(`兑换冷却中，还需等待 ${hours}小时${minutes}分钟`)
      return
    }

    setIsExchanging(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const pointsAddress = CONTRACT_ADDRESSES[97].PointsManagement

      const hash = await walletClient.writeContract({
        address: pointsAddress,
        abi: ABIS.PointsManagement,
        functionName: 'exchangeSalesPointsForUSDT',
        args: [amountInWei, account],
        account
      })

      toast.success('兑换交易已提交，等待确认...')

      // 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({ hash })

      // 检查交易是否成功
      if (receipt.status === 'reverted') {
        throw new Error('交易被回滚，兑换失败')
      }

      // 检查交易日志，确认兑换是否真正成功
      const exchangeSuccessful = receipt.logs.some(log => {
        try {
          // 检查是否有 PointsExchanged 事件
          const decoded = decodeEventLog({
            abi: ABIS.PointsManagement,
            data: log.data,
            topics: log.topics
          })
          return decoded.eventName === 'PointsExchanged'
        } catch (error) {
          return false
        }
      })

      const exchangeFailed = receipt.logs.some(log => {
        try {
          // 检查是否有 ExchangeFailed 事件
          const decoded = decodeEventLog({
            abi: ABIS.PointsManagement,
            data: log.data,
            topics: log.topics
          })
          return decoded.eventName === 'ExchangeFailed'
        } catch (error) {
          return false
        }
      })

      if (exchangeFailed) {
        throw new Error('兑换失败：可能是平台USDT余额不足或积分来源无效')
      }

      if (!exchangeSuccessful) {
        throw new Error('兑换可能未成功，请检查您的积分余额')
      }

      const usdtAmount = calculatePointsToUSDT(amountInWei);

      toast.success(`成功兑换 ${exchangeAmount} 积分为 ${usdtAmount} USDT`)
      setExchangeAmount('')

      // 刷新数据
      await loadPointsData()
      // 触发全局余额刷新
      if (window.refreshTokenBalances) {
        window.refreshTokenBalances()
      }

    } catch (error) {
      console.error('兑换失败:', error)
      toast.error('兑换失败: ' + (error.message || '未知错误'))
    } finally {
      setIsExchanging(false)
    }
  }

  // 计算可获得的USDT
  const calculateUSDT = (points) => {
    if (!points || points === '0') return '0.00';
    try {
      const pointsInWei = parsePointsInput(points);
      return calculatePointsToUSDT(pointsInWei);
    } catch (error) {
      return '0.00';
    }
  }

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return '从未兑换'
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
  }

  useEffect(() => {
    loadPointsData()
  }, [account, isConnected])

  if (!isConnected) {
    return (
      <div className="sales-points-management">
        <div className="module-header">
          <h3>💰 销售积分管理</h3>
          <p>管理您的销售积分和兑换USDT</p>
        </div>
        <div className="connect-prompt">
          <p>请连接钱包以查看销售积分信息</p>
        </div>
      </div>
    )
  }

  return (
    <div className="sales-points-management">
      <div className="module-header">
        <h3>💰 销售积分管理</h3>
        <p>管理您的销售积分和兑换USDT</p>
      </div>

      {/* 积分余额 */}
      <div className="points-balance">
        <div className="balance-item">
          <div className="balance-label">销售积分</div>
          <div className="balance-value">{pointsData.salesPoints}</div>
          <div className="balance-note">可兑换USDT</div>
        </div>
        <div className="balance-item">
          <div className="balance-label">累计兑换</div>
          <div className="balance-value">{parseFloat(pointsData.totalExchanged).toFixed(2)} USDT</div>
          <div className="balance-note">历史总额</div>
        </div>
      </div>

      {/* 兑换功能 */}
      <div className="exchange-section">
        <h4>积分兑换 USDT</h4>
        <div className="exchange-rate">
          <span>兑换比例：1 积分 = 0.3 USDT</span>
        </div>

        <div className="exchange-form">
          <div className="input-group">
            <input
              type="number"
              placeholder="输入兑换积分数量"
              value={exchangeAmount}
              onChange={(e) => setExchangeAmount(e.target.value)}
              min="1"
              max={pointsData.salesPoints}
            />
            <span className="input-suffix">积分</span>
          </div>

          {exchangeAmount && (
            <div className="exchange-preview">
              将获得：{calculateUSDT(exchangeAmount)} USDT
            </div>
          )}

          <button
            className="exchange-btn"
            onClick={handleExchange}
            disabled={isExchanging || !exchangeAmount || parseInt(exchangeAmount) < 1}
          >
            {isExchanging ? '兑换中...' : '立即兑换'}
          </button>
        </div>

        <div className="exchange-info">
          <div className="info-item">
            <span>上次兑换时间：</span>
            <span>{formatTime(pointsData.lastExchangeTime)}</span>
          </div>
          <div className="info-item">
            <span>可兑换积分：</span>
            <span>{pointsData.salesPoints}</span>
          </div>
          <div className="info-note">
            * 每24小时只能兑换一次
          </div>
        </div>
      </div>

      {/* 积分说明 */}
      <div className="points-info">
        <h4>📖 销售积分说明</h4>
        <div className="info-list">
          <div className="info-item-desc">• 销售积分通过商品销售获得</div>
          <div className="info-item-desc">• 只有认证商家才能获得销售积分</div>
          <div className="info-item-desc">• 销售积分可以兑换USDT，比例为1:0.3</div>
          <div className="info-item-desc">• 每24小时只能兑换一次，请合理安排</div>
        </div>
      </div>
    </div>
  )
}

// src/utils/adminPermissionChecker.js
// 管理员权限检查工具

import { readContract } from 'wagmi/actions';
import { config } from '@/wagmi.config';
import { getContractInfo } from '@/apis/admin/utils';

/**
 * 检查当前用户的管理员权限
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} 权限检查结果
 */
export async function checkAdminPermissions(userAddress) {
  try {
    // 只在调试模式下输出日志
    if (import.meta.env.VITE_ENABLE_DEBUG_LOGS === 'true') {
      console.log('🔐 [AdminPermissionChecker] 开始检查管理员权限...');
      console.log('👤 [AdminPermissionChecker] 当前用户地址:', userAddress);
    }

    // 使用全局wagmi配置
    const results = {
      userAddress,
      permissions: {},
      recommendations: []
    };

    // 1. 检查MerchantManagement合约权限
    try {
      const { contractAddress: merchantAddress, contractABI: merchantABI } = await getContractInfo('MerchantManagement');

      // 检查合约owner
      const merchantOwner = await readContract(config, {
        address: merchantAddress,
        abi: merchantABI,
        functionName: 'owner',
        args: [],
      });

      // 检查系统管理员
      let systemAdmin = null;
      let isSystemAdmin = false;

      try {
        systemAdmin = await readContract(config, {
          address: merchantAddress,
          abi: merchantABI,
          functionName: 'getSystemAdmin',
          args: [],
        });
        isSystemAdmin = systemAdmin.toLowerCase() === userAddress.toLowerCase();
      } catch (adminError) {
        console.warn('⚠️ [AdminPermissionChecker] 获取系统管理员失败，可能是旧版本合约:', adminError.message);
      }

      results.permissions.merchantManagement = {
        contractAddress: merchantAddress,
        owner: merchantOwner,
        systemAdmin: systemAdmin,
        isDirectOwner: merchantOwner.toLowerCase() === userAddress.toLowerCase(),
        isSystemAdmin: isSystemAdmin,
        isTimelockOwned: merchantOwner.toLowerCase() === '0x7b01967419de24bee85d270e56a88740aef43c36',
        hasAdminPermission: isSystemAdmin || merchantOwner.toLowerCase() === userAddress.toLowerCase()
      };

      // 只在调试模式下输出权限详情
      if (import.meta.env.VITE_ENABLE_DEBUG_LOGS === 'true') {
        console.log('🏪 [AdminPermissionChecker] MerchantManagement权限:', results.permissions.merchantManagement);
      }

    } catch (error) {
      console.error('❌ [AdminPermissionChecker] 检查MerchantManagement权限失败:', error);
      results.permissions.merchantManagement = { error: error.message };
    }

    // 2. 检查MultiSig合约权限
    try {
      const multisigAddress = '0x2C328453FAede51773C06ECDE921eA8c4d6AD745';
      
      // 这里需要MultiSig合约的ABI来检查是否是owner
      // 暂时跳过，因为我们没有MultiSig的ABI
      results.permissions.multiSig = {
        contractAddress: multisigAddress,
        note: '需要MultiSig合约ABI来检查权限'
      };

    } catch (error) {
      console.error('❌ [AdminPermissionChecker] 检查MultiSig权限失败:', error);
      results.permissions.multiSig = { error: error.message };
    }

    // 3. 生成建议
    const merchantPerms = results.permissions.merchantManagement;

    if (merchantPerms?.hasAdminPermission) {
      results.recommendations.push({
        type: 'has_permission',
        title: '✅ 当前用户具有管理员权限',
        description: merchantPerms.isSystemAdmin ? '当前地址是系统管理员' : '当前地址是合约owner',
        solutions: ['可以直接执行商家管理操作']
      });
    } else {
      if (merchantPerms?.isTimelockOwned && !merchantPerms?.systemAdmin) {
        results.recommendations.push({
          type: 'old_timelock_architecture',
          title: '检测到旧版本合约',
          description: '合约使用Timelock架构但没有系统管理员配置',
          solutions: [
            '升级合约以添加系统管理员功能',
            '通过MultiSig提交Timelock提案',
            '或者重新部署新版本合约'
          ]
        });
      } else if (merchantPerms?.systemAdmin && merchantPerms?.systemAdmin !== '0x0000000000000000000000000000000000000000') {
        results.recommendations.push({
          type: 'has_system_admin',
          title: '合约已配置系统管理员',
          description: `系统管理员: ${merchantPerms.systemAdmin}`,
          solutions: [
            '使用系统管理员地址进行操作',
            '或者通过owner更改系统管理员'
          ]
        });
      } else {
        results.recommendations.push({
          type: 'no_permission',
          title: '当前用户无管理员权限',
          description: '当前地址既不是owner也不是系统管理员',
          solutions: [
            '使用有权限的地址',
            '通过owner设置系统管理员',
            '在开发环境中使用模拟审核'
          ]
        });
      }
    }

    // 只在调试模式下输出完成日志
    if (import.meta.env.VITE_ENABLE_DEBUG_LOGS === 'true') {
      console.log('✅ [AdminPermissionChecker] 权限检查完成:', results);
    }
    return results;

  } catch (error) {
    console.error('❌ [AdminPermissionChecker] 权限检查失败:', error);
    return {
      userAddress,
      error: error.message,
      permissions: {},
      recommendations: []
    };
  }
}

/**
 * 创建一个简化的管理员操作（仅用于开发环境）
 * @param {string} merchantAddress - 商家地址
 * @returns {Promise<Object>} 操作结果
 */
export async function simulateAdminApproval(merchantAddress) {
  try {
    // 只在调试模式下输出日志
    if (import.meta.env.VITE_ENABLE_DEBUG_LOGS === 'true') {
      console.log('🧪 [AdminPermissionChecker] 模拟管理员审核操作...');
      console.log('🏪 [AdminPermissionChecker] 目标商家:', merchantAddress);
    }

    // 在开发环境中，我们可以模拟审核通过
    // 实际上是更新本地状态，而不是调用合约
    
    // 1. 更新本地存储中的商家状态
    const { merchantApplicationStorage } = await import('./merchantApplicationStorage');
    const applicationData = merchantApplicationStorage.getApplication(merchantAddress);
    
    if (applicationData) {
      const updatedData = {
        ...applicationData,
        status: 'approved',
        approvedTime: new Date().toISOString(),
        approvedBy: 'dev-admin'
      };
      
      merchantApplicationStorage.saveApplication(merchantAddress, updatedData);
      
      // 只在调试模式下输出成功日志
      if (import.meta.env.VITE_ENABLE_DEBUG_LOGS === 'true') {
        console.log('✅ [AdminPermissionChecker] 本地状态已更新为已审核');
      }
      
      return {
        success: true,
        type: 'local_simulation',
        message: '开发环境模拟审核成功（仅更新本地状态）',
        merchantAddress,
        updatedData
      };
    } else {
      throw new Error('未找到商家申请数据');
    }

  } catch (error) {
    console.error('❌ [AdminPermissionChecker] 模拟审核失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 获取权限解决方案
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Array>} 解决方案列表
 */
export async function getPermissionSolutions(userAddress) {
  const permissionCheck = await checkAdminPermissions(userAddress);
  
  const solutions = [
    {
      id: 'dev_simulation',
      title: '🧪 开发环境模拟审核',
      description: '在开发环境中模拟审核操作（仅更新本地状态）',
      difficulty: 'easy',
      timeRequired: '立即',
      action: 'simulateAdminApproval'
    },
    {
      id: 'multisig_proposal',
      title: '📝 通过MultiSig提交提案',
      description: '使用MultiSig合约提交Timelock提案来执行审核',
      difficulty: 'hard',
      timeRequired: '10分钟 + 延迟时间',
      action: 'createTimelockProposal'
    },
    {
      id: 'deploy_dev_contract',
      title: '🚀 部署开发版本合约',
      description: '部署一个简化版本的合约，直接设置当前地址为owner',
      difficulty: 'medium',
      timeRequired: '5-10分钟',
      action: 'deployDevContract'
    },
    {
      id: 'use_authorized_address',
      title: '🔑 使用授权地址',
      description: '切换到有权限的地址进行操作',
      difficulty: 'medium',
      timeRequired: '立即',
      action: 'switchToAuthorizedAddress'
    }
  ];

  return solutions;
}

/**
 * 在浏览器控制台中暴露权限检查函数
 */
export function exposePermissionFunctions() {
  window.checkAdminPermissions = checkAdminPermissions;
  window.simulateAdminApproval = simulateAdminApproval;
  window.getPermissionSolutions = getPermissionSolutions;
}

// 自动暴露函数（如果在开发环境中且需要调试）
if (import.meta.env.DEV && import.meta.env.VITE_ENABLE_DEBUG_LOGS === 'true') {
  exposePermissionFunctions();
}

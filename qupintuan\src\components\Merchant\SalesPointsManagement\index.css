/* 销售积分管理组件样式 */
.sales-points-management {
  padding: 24px;
}

/* 模块头部 */
.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.module-header h3 {
  font-size: 20px;
  font-weight: bold;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-header p {
  font-size: 14px;
  color: #64748b;
  margin: 4px 0 0 0;
}

.refresh-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #475569;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #e2e8f0;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 连接提示 */
.connect-prompt {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

/* 积分余额 */
.points-balance {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.balance-item {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  padding: 20px;
  border-radius: 12px;
  color: white;
  text-align: center;
}

.balance-item:nth-child(2) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.balance-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.balance-note {
  font-size: 12px;
  opacity: 0.8;
}

/* 兑换功能 */
.exchange-section {
  background: #f8fafc;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.exchange-section h4 {
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.exchange-rate {
  background: #e0f2fe;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  color: #0369a1;
  font-size: 14px;
  font-weight: 500;
}

.exchange-form {
  margin-bottom: 24px;
}

.input-group {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
}

.input-group input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
}

.input-suffix {
  padding: 12px 16px;
  background: #f9fafb;
  color: #6b7280;
  font-size: 14px;
  border-left: 1px solid #d1d5db;
}

.exchange-preview {
  background: #ecfdf5;
  color: #065f46;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
}

.exchange-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exchange-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.exchange-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 兑换信息 */
.exchange-info {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.info-item:first-child {
  color: #475569;
}

.info-item:last-child {
  color: #059669;
  font-weight: 500;
}

.info-note {
  font-size: 12px;
  color: #f59e0b;
  margin-top: 12px;
  padding: 8px 12px;
  background: #fffbeb;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
}

/* 积分说明 */
.points-info {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.points-info h4 {
  font-size: 16px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item-desc {
  font-size: 14px;
  color: #475569;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sales-points-management {
    padding: 16px;
  }
  
  .module-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .points-balance {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .balance-item {
    padding: 16px;
  }
  
  .balance-value {
    font-size: 20px;
  }
  
  .exchange-section {
    padding: 16px;
  }

  /* 确保输入框在平板端也保持横向布局 */
  .input-group {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
  }

  .input-group input {
    flex: 1 !important;
    min-width: 0;
  }

  .input-suffix {
    flex-shrink: 0 !important;
    white-space: nowrap;
  }

  .points-info {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .sales-points-management {
    padding: 12px;
  }

  .exchange-section {
    padding: 12px;
  }

  /* 确保输入框在移动端保持横向布局 */
  .input-group {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    width: 100%;
    min-height: 44px; /* 确保足够的触摸区域 */
  }

  .input-group input {
    flex: 1 !important;
    padding: 10px 12px;
    font-size: 14px;
    border: none;
    outline: none;
    min-width: 0; /* 防止flex子项溢出 */
  }

  .input-suffix {
    flex-shrink: 0 !important;
    padding: 10px 12px;
    font-size: 12px;
    background: #f9fafb;
    border-left: 1px solid #d1d5db;
    white-space: nowrap;
  }

  .exchange-btn {
    padding: 12px;
    font-size: 14px;
  }

  .points-info {
    padding: 12px;
  }
}

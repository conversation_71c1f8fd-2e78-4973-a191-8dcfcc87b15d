// src/components/Profile/AddressManagement/index.jsx
import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { toast } from 'react-hot-toast'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { createPublicClient, createWalletClient, custom, http } from 'viem'
import { bscTestnet } from 'viem/chains'
import './index.css'

export default function AddressManagement() {
  const { address: account, isConnected } = useAccount()

  // 状态管理
  const [addresses, setAddresses] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAddress, setEditingAddress] = useState(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    isDefault: false
  })

  // 创建客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  })

  // 加载用户地址列表
  const loadAddresses = async () => {
    if (!account || !isConnected) {
      return
    }

    setIsLoading(true)
    try {
      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      // 尝试使用新的 getMyAddresses 函数
      try {
        const userAddresses = await publicClient.readContract({
          address: addressAddress,
          abi: ABIS.AddressManagement,
          functionName: 'getMyAddresses',
          args: [],
          account
        })
        setAddresses(userAddresses || [])
      } catch (newFunctionError) {
        // 如果新函数不存在，回退到旧函数
        try {
          const userAddresses = await publicClient.readContract({
            address: addressAddress,
            abi: ABIS.AddressManagement,
            functionName: 'getUserAddresses',
            args: [account]
          })
          setAddresses(userAddresses || [])
        } catch (authError) {
          // 如果权限错误，说明合约需要更新
          toast.error('地址管理功能需要合约升级，请联系管理员')
          setAddresses([])
        }
      }
    } catch (error) {
      toast.error('加载地址列表失败: ' + (error.message || '未知错误'))
      setAddresses([])
    } finally {
      setIsLoading(false)
    }
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    })
    setEditingAddress(null)
    setShowAddForm(false)
  }

  // 添加地址
  const handleAddAddress = async () => {
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      // 如果是第一个地址，强制设为默认
      const isFirstAddress = addresses.length === 0;
      const shouldBeDefault = isFirstAddress || formData.isDefault;

      const hash = await walletClient.writeContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'addAddress',
        args: [
          formData.name,
          formData.phone,
          formData.province,
          formData.city,
          formData.district,
          formData.detail,
          shouldBeDefault
        ],
        account
      })

      toast.success('正在添加地址...')

      // 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        timeout: 120000 // 2分钟超时
      })

      if (receipt.status === 'success') {
        toast.success('地址添加成功！')
        resetForm()
        await loadAddresses()
      } else {
        toast.error('交易执行失败')
      }

    } catch (error) {
      // 详细的错误分析
      if (error.message.includes('User rejected')) {
        toast.error('您取消了交易')
      } else if (error.message.includes('insufficient funds')) {
        toast.error('余额不足，无法支付Gas费用')
      } else if (error.message.includes('execution reverted')) {
        if (error.message.includes('No contracts')) {
          toast.error('合约不允许此操作，请联系管理员')
        } else if (error.message.includes('Pausable: paused')) {
          toast.error('合约已暂停，请稍后重试')
        } else {
          toast.error('合约执行失败，请检查参数')
        }
      } else {
        toast.error(`添加地址失败: ${error.message}`)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // 更新地址
  const handleUpdateAddress = async () => {
    if (!validateForm() || editingAddress === null) return

    setIsSubmitting(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      const hash = await walletClient.writeContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'updateAddress',
        args: [
          editingAddress,
          formData.name,
          formData.phone,
          formData.province,
          formData.city,
          formData.district,
          formData.detail,
          formData.isDefault
        ],
        account
      })

      toast.success('更新地址交易已提交，等待确认...')

      // 等待交易确认
      await publicClient.waitForTransactionReceipt({ hash })

      toast.success('地址更新成功！')
      resetForm()
      await loadAddresses()

    } catch (error) {
      toast.error('更新地址失败: ' + (error.message || '未知错误'))
    } finally {
      setIsSubmitting(false)
    }
  }

  // 删除地址
  const handleDeleteAddress = async (addressId) => {
    if (!confirm('确定要删除这个地址吗？')) return

    setIsSubmitting(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      const hash = await walletClient.writeContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'deleteAddress',
        args: [addressId],
        account
      })

      toast.success('删除地址交易已提交，等待确认...')

      // 等待交易确认
      await publicClient.waitForTransactionReceipt({ hash })

      toast.success('地址删除成功！')
      await loadAddresses()

    } catch (error) {
      toast.error('删除地址失败: ' + (error.message || '未知错误'))
    } finally {
      setIsSubmitting(false)
    }
  }

  // 编辑地址
  const handleEditAddress = (address) => {
    setFormData({
      name: address.name,
      phone: address.phone,
      province: address.province,
      city: address.city,
      district: address.district,
      detail: address.detail,
      isDefault: address.isDefault
    })
    setEditingAddress(Number(address.addressId))
    setShowAddForm(true)
  }

  // 表单验证
  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error('请输入收货人姓名')
      return false
    }
    if (!formData.phone.trim()) {
      toast.error('请输入联系电话')
      return false
    }
    if (!formData.province.trim()) {
      toast.error('请输入省份')
      return false
    }
    if (!formData.city.trim()) {
      toast.error('请输入城市')
      return false
    }
    if (!formData.district.trim()) {
      toast.error('请输入区县')
      return false
    }
    if (!formData.detail.trim()) {
      toast.error('请输入详细地址')
      return false
    }
    return true
  }

  // 格式化地址显示
  const formatAddress = (address) => {
    return `${address.province} ${address.city} ${address.district} ${address.detail}`
  }

  // 设置默认地址
  const handleSetDefaultAddress = async (addressId) => {
    if (!account || !isConnected) {
      toast.error('请先连接钱包')
      return
    }

    setIsSubmitting(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      const hash = await walletClient.writeContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'setDefaultAddress',
        args: [addressId],
        account
      })

      toast.success('正在设置默认地址...')

      // 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        timeout: 120000
      })

      if (receipt.status === 'success') {
        toast.success('默认地址设置成功！')
        await loadAddresses() // 重新加载地址列表
      } else {
        throw new Error('交易失败')
      }

    } catch (error) {
      toast.error('设置默认地址失败: ' + (error.message || '未知错误'))
    } finally {
      setIsSubmitting(false)
    }
  }





  useEffect(() => {
    loadAddresses()
  }, [account, isConnected])

  if (!isConnected) {
    return (
      <div className="address-management">
        <div className="section-header">
          <div className="header-content">
            <h3>📍 收货地址</h3>
            <p>管理您的收货地址信息</p>
          </div>
        </div>
        <div className="address-content">
          <div className="connect-prompt">
            <p>请连接钱包以管理收货地址</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="address-management">
      <div className="section-header">
        <div className="header-content">
          <h3>📍 收货地址</h3>
          <p>管理您的收货地址信息</p>
        </div>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={loadAddresses}
            disabled={isLoading}
          >
            {isLoading ? '🔄' : '🔄 刷新'}
          </button>
          <button
            className="add-btn"
            onClick={() => setShowAddForm(true)}
            disabled={addresses.length >= 20}
          >
            ➕ 添加地址
          </button>
        </div>
      </div>

      {/* 地址使用规则说明 */}
      <div className="address-rules">
        <div className="rules-header">
          <h4>📋 地址使用规则</h4>
        </div>
        <div className="rules-content">
          <div className="rule-item">
            <span className="rule-icon">🎯</span>
            <div className="rule-text">
              <strong>默认地址机制：</strong>
              购买商品时系统将自动使用您的默认收货地址，确保交易安全可靠。
            </div>
          </div>
          <div className="rule-item">
            <span className="rule-icon">🔄</span>
            <div className="rule-text">
              <strong>地址切换：</strong>
              如需使用其他收货地址，请先将其设置为默认地址，然后再进行购买。
            </div>
          </div>
          <div className="rule-item">
            <span className="rule-icon">⚡</span>
            <div className="rule-text">
              <strong>快速切换：</strong>
              设置默认地址只需与区块链交互一次，通常几秒钟即可完成。
            </div>
          </div>
          <div className="rule-item">
            <span className="rule-icon">✅</span>
            <div className="rule-text">
              <strong>首次添加：</strong>
              您的第一个地址将自动设置为默认地址，无需手动勾选。
            </div>
          </div>
        </div>
      </div>

      {/* 地址内容区域 */}
      <div className="address-content">
        <div className="address-list">
        {isLoading ? (
          <div className="loading-container">
            <div className="loading-spinner">🔄</div>
            <p>正在加载地址列表...</p>
          </div>
        ) : addresses.length === 0 ? (
          <div className="empty-container">
            <div className="empty-icon">📍</div>
            <h3>暂无收货地址</h3>
          </div>
        ) : (
          addresses.map((address, index) => (
            <div key={index} className="address-card">
              <div className="address-header">
                <div className="address-info">
                  <span className="address-name">{address.name}</span>
                  <span className="address-phone">{address.phone}</span>
                  {address.isDefault && (
                    <span className="default-badge">默认</span>
                  )}
                </div>
                <div className="address-actions">
                  {!address.isDefault && (
                    <button
                      className="set-default-btn"
                      onClick={() => handleSetDefaultAddress(Number(address.addressId))}
                      disabled={isSubmitting}
                    >
                      设为默认
                    </button>
                  )}
                  <button
                    className="edit-btn"
                    onClick={() => handleEditAddress(address)}
                  >
                    编辑
                  </button>
                  <button
                    className="delete-btn"
                    onClick={() => handleDeleteAddress(Number(address.addressId))}
                    disabled={isSubmitting}
                  >
                    删除
                  </button>
                </div>
              </div>
              <div className="address-detail">
                {formatAddress(address)}
              </div>
            </div>
          ))
        )}
        </div>
      </div>



      {/* 添加/编辑表单弹窗 */}
      {showAddForm && (
        <div className="form-overlay">
          <div className="form-modal">
            <div className="form-header">
              <h4>{editingAddress !== null ? '编辑地址' : '添加地址'}</h4>
              <button className="close-btn" onClick={resetForm}>✕</button>
            </div>

            <div className="form-content">
              <div className="form-row">
                <div className="form-group">
                  <label>收货人姓名 *</label>
                  <input
                    type="text"
                    placeholder="请输入收货人姓名"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    maxLength={64}
                  />
                </div>
                <div className="form-group">
                  <label>联系电话 *</label>
                  <input
                    type="tel"
                    placeholder="请输入联系电话"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    maxLength={32}
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>省份 *</label>
                  <input
                    type="text"
                    placeholder="请输入省份"
                    value={formData.province}
                    onChange={(e) => setFormData(prev => ({ ...prev, province: e.target.value }))}
                    maxLength={64}
                  />
                </div>
                <div className="form-group">
                  <label>城市 *</label>
                  <input
                    type="text"
                    placeholder="请输入城市"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    maxLength={64}
                  />
                </div>
              </div>

              <div className="form-group">
                <label>区县 *</label>
                <input
                  type="text"
                  placeholder="请输入区县"
                  value={formData.district}
                  onChange={(e) => setFormData(prev => ({ ...prev, district: e.target.value }))}
                  maxLength={64}
                />
              </div>

              <div className="form-group">
                <label>详细地址 *</label>
                <textarea
                  placeholder="请输入详细地址"
                  value={formData.detail}
                  onChange={(e) => setFormData(prev => ({ ...prev, detail: e.target.value }))}
                  maxLength={256}
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.isDefault || addresses.length === 0}
                    onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                    disabled={addresses.length === 0}
                  />
                  设为默认地址
                  {addresses.length === 0 && (
                    <span className="auto-default-tip">（首个地址自动设为默认）</span>
                  )}
                </label>
                {addresses.length > 0 && (
                  <div className="default-address-tip">
                    💡 购买商品时将使用默认地址，如需使用此地址购买请勾选此选项
                  </div>
                )}
              </div>
            </div>

            <div className="form-actions">
              <button className="cancel-btn" onClick={resetForm}>
                取消
              </button>
              <button
                className="submit-btn"
                onClick={editingAddress !== null ? handleUpdateAddress : handleAddAddress}
                disabled={isSubmitting}
              >
                {isSubmitting ? '提交中...' : editingAddress !== null ? '更新地址' : '添加地址'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
